import type { Database } from "$lib/database.types";
import type { Track } from "$lib/types/track";
import type { SupabaseClient } from "@supabase/supabase-js";

export class TrackRepository {
  private supabase: SupabaseClient<Database>;

  constructor(supabase: SupabaseClient<Database>) {
    this.supabase = supabase;
  }

  /**
   * Pobiera utwór po ID
   * @param trackId ID utworu
   * @returns Dane utworu
   */
  async getTrackById(trackId: number): Promise<{
    data: Track | null;
    error: any;
  }> {
    return await this.supabase
      .from("tracks")
      .select("*")
      .eq("id", trackId)
      .single();
  }

  /**
   * Pobiera utwór po slugu
   * @param slug Slug utworu
   * @returns Dane utworu
   */
  async getTrackBySlug(slug: string): Promise<{
    data: Track | null;
    error: any;
  }> {
    return await this.supabase
      .from("tracks")
      .select("*")
      .eq("slug", slug)
      .single();
  }

  /**
   * Sprawdza, czy slug utworu jest unikalny
   * @param slug Slug do sprawdzenia
   * @param excludeId ID utworu do wykluczenia ze sprawdzania (opcjonalnie)
   * @returns Informacja, czy slug jest unikalny
   */
  async isTrackSlugUnique(slug: string, excludeId?: number): Promise<boolean> {
    let query = this.supabase.from("tracks").select("id").eq("slug", slug);

    if (excludeId) {
      query = query.neq("id", excludeId);
    }

    const { data } = await query;
    return !data || data.length === 0;
  }

  /**
   * Tworzy nowy utwór
   * @param track Dane utworu
   * @returns Utworzony utwór
   */
  async createTrack(track: {
    name: string;
    project_id: number;
    uploaded_by: string;
    slug?: string;
  }): Promise<{
    data: Track | null;
    error: any;
  }> {
    return await this.supabase
      .from("tracks")
      .insert([track])
      .select("*")
      .single();
  }

  /**
   * Aktualizuje utwór
   * @param trackId ID utworu
   * @param track Dane utworu do aktualizacji
   * @returns Zaktualizowany utwór
   */
  async updateTrack(
    trackId: number,
    track: Partial<Track>
  ): Promise<{
    data: Track | null;
    error: any;
  }> {
    return await this.supabase
      .from("tracks")
      .update(track)
      .eq("id", trackId)
      .select("*")
      .single();
  }
}
