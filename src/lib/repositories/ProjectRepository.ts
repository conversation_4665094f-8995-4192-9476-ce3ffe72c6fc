import type { Database } from "$lib/database.types";
import type { Project } from "$lib/types/project";
import type { Track } from "$lib/types/track";
import type { User } from "$lib/types/user";
import type { SupabaseClient } from "@supabase/supabase-js";

export class ProjectRepository {
  private supabase: SupabaseClient<Database>;

  constructor(supabase: SupabaseClient<Database>) {
    this.supabase = supabase;
  }

  /**
   * Pobiera projekty użytkownika wraz z liczbą członków
   * @param userId ID użytkownika
   * @returns Lista projektów z liczbą członków i opcjonalnie z informacjami o członkach
   */
  async getUserProjects(userId: string) {
    console.log("userId", userId);

    // Najpierw pobieramy ID projektów, do których należy użytkownik
    const { data: projectIds, error: projectIdsError } = await this.supabase
      .from("projects_users")
      .select("project_id")
      .eq("user_id", userId);

    if (projectIdsError) {
      console.error("Error fetching project IDs:", projectIdsError);
      return { data: null, error: projectIdsError };
    }

    // Jeśli nie znaleziono projektów, zwracamy pustą tablicę
    if (!projectIds || projectIds.length === 0) {
      return { data: [], error: null };
    }

    // Pobieramy projekty na podstawie znalezionych ID
    const { data, error } = await this.supabase
      .from("projects")
      .select("*,members:projects_users(user:user_id(*))")
      .in(
        "id",
        projectIds.map((item) => item.project_id)
      );

    console.log("data:", data);

    return { data, error };
  }

  /**
   * Pobiera projekt po ID
   * @param projectId ID projektu
   * @returns Dane projektu
   */
  async getProjectById(projectId: number): Promise<{
    data: Project | null;
    error: any;
  }> {
    return await this.supabase
      .from("projects")
      .select("*")
      .eq("id", projectId)
      .single();
  }

  /**
   * Pobiera projekt po slugu
   * @param slug Slug projektu
   * @returns Dane projektu
   */
  async getProjectBySlug(slug: string): Promise<{
    data: Project | null;
    error: any;
  }> {
    return await this.supabase
      .from("projects")
      .select("*")
      .eq("slug", slug)
      .single();
  }

  /**
   * Sprawdza, czy slug projektu jest unikalny
   * @param slug Slug do sprawdzenia
   * @param excludeId ID projektu do wykluczenia ze sprawdzania (opcjonalnie)
   * @returns Informacja, czy slug jest unikalny
   */
  async isProjectSlugUnique(
    slug: string,
    excludeId?: number
  ): Promise<boolean> {
    let query = this.supabase.from("projects").select("id").eq("slug", slug);

    if (excludeId) {
      query = query.neq("id", excludeId);
    }

    const { data } = await query;
    return !data || data.length === 0;
  }

  /**
   * Pobiera ostatnie utwory dla projektu
   * @param projectId ID projektu
   * @param limit Maksymalna liczba utworów do pobrania
   * @returns Lista utworów
   */
  async getRecentTracks(
    projectId: number,
    limit: number = 3
  ): Promise<{
    data: Track[] | null;
    error: any;
  }> {
    return await this.supabase
      .from("tracks")
      .select()
      .eq("project_id", projectId)
      .order("created_at", { ascending: false })
      .limit(limit);
  }

  /**
   * Pobiera wszystkie utwory projektu
   * @param projectId ID projektu
   * @returns Lista wszystkich utworów projektu
   */
  async getAllProjectTracks(projectId: number): Promise<{
    data: Track[] | null;
    error: any;
  }> {
    return await this.supabase
      .from("tracks")
      .select("*")
      .eq("project_id", projectId)
      .order("created_at", { ascending: false });
  }

  /**
   * Pobiera członków projektu
   * @param projectId ID projektu
   * @param limit Maksymalna liczba członków do pobrania
   * @returns Lista członków projektu
   */
  async getProjectMembers(
    projectId: number,
    limit: number = 5
  ): Promise<{
    data: { user: User }[] | null;
    error: any;
  }> {
    return await this.supabase
      .from("projects_users")
      .select(
        `
        user:user_id(*)
      `
      )
      .eq("project_id", projectId)
      .limit(limit);
  }

  /**
   * Tworzy nowy projekt
   * @param project Dane projektu
   * @returns Utworzony projekt
   */
  async createProject(project: { name: string; slug?: string }): Promise<{
    data: Project | null;
    error: any;
  }> {
    return await this.supabase
      .from("projects")
      .insert([project])
      .select("*")
      .single();
  }

  /**
   * Aktualizuje projekt
   * @param projectId ID projektu
   * @param project Dane projektu do aktualizacji
   * @returns Zaktualizowany projekt
   */
  async updateProject(
    projectId: number,
    project: Partial<Project>
  ): Promise<{
    data: Project | null;
    error: any;
  }> {
    return await this.supabase
      .from("projects")
      .update(project)
      .eq("id", projectId)
      .select("*")
      .single();
  }

  /**
   * Dodaje użytkownika do projektu
   * @param projectId ID projektu
   * @param userId ID użytkownika
   * @returns Informacja o powodzeniu operacji
   */
  async addUserToProject(
    projectId: number,
    userId: string
  ): Promise<{
    data: any;
    error: any;
  }> {
    return await this.supabase
      .from("projects_users")
      .insert([{ project_id: projectId, user_id: userId }]);
  }

  /**
   * Sprawdza, czy użytkownik ma dostęp do projektu
   * @param projectId ID projektu
   * @param userId ID użytkownika
   * @returns Informacja o dostępie użytkownika do projektu
   */
  async checkUserProjectAccess(
    projectId: number,
    userId: string
  ): Promise<{
    data: any;
    error: any;
    hasAccess: boolean;
  }> {
    const { data, error } = await this.supabase
      .from("projects_users")
      .select("*")
      .eq("project_id", projectId)
      .eq("user_id", userId)
      .single();

    return {
      data,
      error,
      hasAccess: !!data && !error,
    };
  }

  /**
   * Usuwa projekt i wszystkie powiązane dane
   * @param projectId ID projektu
   * @param slug Slug projektu (potrzebny do usunięcia plików)
   * @returns Informacja o powodzeniu operacji
   */
  async deleteProject(
    projectId: number,
    slug: string
  ): Promise<{
    data: any;
    error: any;
  }> {
    // 1. Pobierz listę wszystkich plików w storage dla projektu
    const { data: filesList, error: listError } = await this.supabase.storage
      .from("project_files")
      .list(slug);

    if (listError) {
      console.error("Błąd podczas pobierania listy plików:", listError);
      return { data: null, error: listError };
    }

    // 2. Utwórz tablicę ścieżek do plików
    const filePaths = filesList.map((file) => `${slug}/${file.name}`);

    // 3. Usuń wszystkie pliki jednocześnie
    if (filePaths.length > 0) {
      const { error: storageError } = await this.supabase.storage
        .from("project_files")
        .remove(filePaths);

      if (storageError) {
        console.error("Błąd podczas usuwania plików:", storageError);
        return { data: null, error: storageError };
      }
    }

    // 4. Usuń rekord projektu z bazy danych
    return await this.supabase.from("projects").delete().eq("id", projectId);
  }
}
