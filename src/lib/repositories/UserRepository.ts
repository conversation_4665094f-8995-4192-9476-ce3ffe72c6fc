import type { Database } from "$lib/database.types";
import type { User } from "$lib/types/user";
import type { SupabaseClient } from "@supabase/supabase-js";

export class UserRepository {
  private supabase: SupabaseClient<Database>;

  constructor(supabase: SupabaseClient<Database>) {
    this.supabase = supabase;
  }

  /**
   * Pobiera użytkownika po ID
   * @param userId ID użytkownika
   * @returns Dane użytkownika
   */
  async getUserById(userId: string): Promise<{
    data: User | null;
    error: any;
  }> {
    return await this.supabase
      .from("users")
      .select("*")
      .eq("id", userId)
      .single();
  }

  /**
   * Tworzy nowego użytkownika
   * @param user Dane uż<PERSON>
   * @returns Utworzony użytkownik
   */
  async createUser(user: {
    id: string;
    email: string | null;
    name?: string | null;
    avatar_url?: string | null;
  }): Promise<{
    data: User | null;
    error: any;
  }> {
    return await this.supabase
      .from("users")
      .insert([
        {
          id: user.id,
          email: user.email,
          name: user.name || (user.email ? user.email.split("@")[0] : null),
          avatar_url: user.avatar_url || null,
          created_at: new Date().toISOString(),
        },
      ])
      .select()
      .single();
  }

  /**
   * Aktualizuje dane użytkownika
   * @param userId ID użytkownika
   * @param userData Dane do aktualizacji
   * @returns Zaktualizowany użytkownik
   */
  async updateUser(
    userId: string,
    userData: Partial<User>
  ): Promise<{
    data: User | null;
    error: any;
  }> {
    return await this.supabase
      .from("users")
      .update(userData)
      .eq("id", userId)
      .select()
      .single();
  }
}
