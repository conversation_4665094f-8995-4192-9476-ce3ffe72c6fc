<script lang="ts">
  import Button from "$lib/components/ui/Button.svelte";
  import Modal from "$lib/components/ui/Modal.svelte";
  import { toast } from "$lib/components/ui/toast";
  import type { Track } from "$lib/types/track";

  let {
    isOpen = $bindable(),
    track,
    onClose,
  }: { isOpen: boolean; track: Track | null; onClose: () => void } = $props();

  let isLoading = $state(false);

  async function handleDelete() {
    if (!track) return;

    isLoading = true;

    try {
      // Usuwanie utworu za pomocą endpointu API
      const response = await fetch(`/api/tracks/${track.id}`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || "Wystąpił błąd podczas usuwania utworu"
        );
      }

      toast.success("Utwór został pomyślnie usunięty");
      onClose?.();
    } catch (err) {
      console.error("Błąd podczas usuwania utworu:", err);
      toast.error(
        err instanceof Error
          ? err.message
          : "Wystąpił błąd podczas usuwania utworu"
      );
    } finally {
      isLoading = false;
    }
  }
</script>

<Modal bind:isOpen title="Usuń utwór" {isLoading}>
  <p class="text-gray-300 mb-6">
    Czy na pewno chcesz usunąć utwór
    <span class="font-semibold">{track?.name}</span>? Tej czynności nie można
    cofnąć. Wszystkie pliki utworu zostaną trwale usunięte.
  </p>
  <div class="flex justify-end space-x-3 mt-6">
    <Button
      variant="normal"
      type="button"
      onclick={() => {
        isOpen = false;
      }}
      disabled={isLoading}
    >
      Anuluj
    </Button>
    <Button
      variant="danger"
      onclick={handleDelete}
      disabled={isLoading || !track}
      {isLoading}
    >
      Potwierdź usunięcie
    </Button>
  </div>
</Modal>
