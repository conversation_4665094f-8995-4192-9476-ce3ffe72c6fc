<script lang="ts">
  import { goto } from "$app/navigation";
  import Button from "$lib/components/ui/Button.svelte";
  import Input from "$lib/components/ui/Input.svelte";
  import Modal from "$lib/components/ui/Modal.svelte";
  import { toast } from "$lib/components/ui/toast";
  let { isOpen = $bindable(), "on:project-created": projectCreated } = $props<{
    isOpen?: boolean;
  }>();

  let newProjectName = $state("");
  let isLoading = $state(false);
  let error = $state("");

  function reset() {
    newProjectName = "";
    isLoading = false;
    error = "";
  }

  function validateForm() {
    if (!newProjectName.trim()) {
      error = "Nazwa projektu jest wymagana";
      return false;
    }
    error = "";
    return true;
  }

  async function handleSubmit(event: SubmitEvent) {
    event.preventDefault();

    if (!validateForm()) {
      return;
    }

    isLoading = true;

    try {
      const response = await fetch("/api/projects", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          name: newProjectName.trim(),
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(
          data.message || "Wystąpił błąd podczas tworzenia projektu"
        );
      }

      // Najpierw przekierowanie do nowo utworzonego projektu
      if (data.project && data.project.slug) {
        // Wywołanie callbacka przed przekierowaniem
        projectCreated?.();

        // Bezpośrednie przekierowanie bez zamykania modalu
        goto(`/${data.project.slug}`);
      } else {
        // Jeśli z jakiegoś powodu nie ma sluga, zamykamy modal
        isOpen = false;
        reset();
      }
    } catch (err) {
      console.error("Błąd podczas tworzenia projektu:", err);
      toast.error(
        err instanceof Error
          ? err.message
          : "Wystąpił błąd podczas tworzenia projektu"
      );

      // Resetujemy stan ładowania tylko w przypadku błędu
      isLoading = false;
    }
  }
</script>

<Modal
  bind:isOpen
  title="Nowy projekt"
  {isLoading}
  size="sm"
  onClose={() => reset()}
>
  <form method="POST" onsubmit={handleSubmit}>
    <Input
      type="text"
      name="name"
      label="Nazwa projektu"
      bind:value={newProjectName}
      placeholder="Wprowadź nazwę projektu"
      autoFocus={true}
      {error}
      oninput={() => error && (error = "")}
    />

    <div class="flex justify-end space-x-3 mt-6">
      <Button
        variant="normal"
        type="button"
        onclick={() => {
          isOpen = false;
          reset();
        }}
        disabled={isLoading}
      >
        Anuluj
      </Button>
      <Button
        primary
        {isLoading}
        type="submit"
        class="disabled:opacity-50 disabled:cursor-not-allowed"
        disabled={!newProjectName.trim() || isLoading}
      >
        Utwórz projekt
      </Button>
    </div>
  </form>
</Modal>
