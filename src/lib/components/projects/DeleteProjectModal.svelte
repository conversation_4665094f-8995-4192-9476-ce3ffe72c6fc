<script lang="ts">
  import { goto } from "$app/navigation";
  import Button from "$lib/components/ui/Button.svelte";
  import Modal from "$lib/components/ui/Modal.svelte";
  import { toast } from "$lib/components/ui/toast";
  import type { Project } from "$lib/types/project";

  let { isOpen = $bindable(), project }: { isOpen: boolean; project: Project } =
    $props();
  let isLoading = $state(false);

  async function handleDelete() {
    isLoading = true;

    try {
      const response = await fetch(`/api/projects/${project.id}`, {
        method: "DELETE",
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(
          data.message || "Wystąpił błąd podczas usuwania projektu"
        );
      }

      toast.success("Projekt został pomyślnie usunięty");

      // Najpierw przekierowanie na stronę główną
      goto("/dashboard");

      // Modal zostanie automatycznie zamknięty podczas przekierowania
    } catch (err) {
      console.error("Błąd podczas usuwania projektu:", err);
      toast.error(
        err instanceof Error
          ? err.message
          : "Wystąpił błąd podczas usuwania projektu"
      );

      // Resetujemy stan ładowania tylko w przypadku błędu
      isLoading = false;
    }
  }
</script>

<Modal bind:isOpen title="Usuń projekt" {isLoading}>
  <p class="text-gray-300 mb-6">
    Czy na pewno chcesz usunąć projekt
    <span class="font-semibold">{project.name}</span>? Tej czynności nie można
    cofnąć. Wszystkie pliki projektu i dane zostaną trwale usunięte.
  </p>
  <div class="flex justify-end space-x-3 mt-6">
    <Button
      variant="normal"
      type="button"
      onclick={() => {
        isOpen = false;
      }}
      disabled={isLoading}
    >
      Anuluj
    </Button>
    <Button
      variant="danger"
      onclick={handleDelete}
      disabled={isLoading}
      {isLoading}
    >
      Potwierdź usunięcie
    </Button>
  </div>
</Modal>
