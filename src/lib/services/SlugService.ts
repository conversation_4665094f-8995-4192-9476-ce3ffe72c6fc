import type { Database } from "$lib/database.types";
import type { SupabaseClient } from "@supabase/supabase-js";

type TableNames = keyof Database["public"]["Tables"];

export class SlugService {
  private supabase: SupabaseClient<Database>;

  constructor(supabase: SupabaseClient<Database>) {
    this.supabase = supabase;
  }

  /**
   * Generuje unikalny slug na podstawie nazwy
   * @param tableName Nazwa tabeli, dla której generowany jest slug
   * @param baseName Nazwa bazowa, z której generowany jest slug
   * @returns Unikalny slug
   */
  async generateUniqueSlug(
    tableName: TableNames,
    baseName: string
  ): Promise<string> {
    // Walidacja parametrów wejściowych
    if (!tableName || tableName.trim() === "") {
      throw new Error("Nazwa tabeli nie może być pusta");
    }

    let newSlug: string;

    // Obsługa pustych nazw bazowych
    if (!baseName || baseName.trim() === "") {
      newSlug = tableName === "projects" ? "project" : "track";
    } else {
      // Przetwarzanie normalnej nazwy
      // 1. Przytnij białe znaki
      newSlug = baseName.trim();
      // 2. Konwertuj na małe litery
      newSlug = newSlug.toLowerCase();
      // 3. Zamień znaki specjalne na myślniki
      newSlug = newSlug.replace(/[^a-z0-9-]/g, "-");
      // 4. Zamień wielokrotne myślniki na pojedyncze
      newSlug = newSlug.replace(/-+/g, "-");
      // 5. Usuń myślniki z początku i końca
      newSlug = newSlug.replace(/^-+|-+$/g, "");
      // 6. Ogranicz długość
      newSlug = newSlug.substring(0, 50);

      // Dodatkowa weryfikacja po wszystkich przekształceniach
      if (newSlug === "") {
        newSlug = tableName === "projects" ? "project" : "track";
      }
    }

    // Sprawdzenie unikalności sluga
    let counter = 1;
    let uniqueSlug = newSlug;
    let isUnique = false;
    const maxAttempts = 100;

    while (!isUnique && counter <= maxAttempts) {
      // Sprawdź, czy slug istnieje w bazie danych
      const { data } = await this.supabase
        .from(tableName)
        .select("slug")
        .or(`slug.eq.${uniqueSlug},slug.like.${uniqueSlug}-%`)
        .limit(1);

      if (!data || data.length === 0) {
        isUnique = true;
      } else {
        // Dodaj licznik do sluga
        uniqueSlug = newSlug.replace(/-\d+$/, "") + "-" + counter;
        counter++;
      }
    }

    // Jako ostateczność, dodaj losowy identyfikator
    if (!isUnique) {
      // Generujemy losowy identyfikator
      const randomId = Math.random().toString(36).substring(2, 10);
      uniqueSlug = newSlug + "-" + randomId;
    }

    return uniqueSlug;
  }
}
