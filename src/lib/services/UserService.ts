import type { UserRepository } from "$lib/repositories/UserRepository";
import type { User } from "$lib/types/user";

export class UserService {
  private userRepository: UserRepository;

  constructor(userRepository: UserRepository) {
    this.userRepository = userRepository;
  }

  /**
   * Pobiera użytkownika po ID
   * @param userId ID użytkownika
   * @returns Dane użytkownika
   */
  async getUserById(userId: string): Promise<User> {
    const { data, error } = await this.userRepository.getUserById(userId);

    if (error) {
      console.error("Error fetching user:", error);
      throw new Error(error.message);
    }

    if (!data) {
      throw new Error("Użytkownik nie został znaleziony");
    }

    return data;
  }

  /**
   * Tworzy nowego użytkownika po rejestracji
   * @param userData Dane użytkownika
   * @returns Utwo<PERSON><PERSON> użyt<PERSON>wnik
   */
  async createUser(userData: {
    id: string;
    email: string | null;
    name?: string | null;
    avatar_url?: string | null;
  }): Promise<User> {
    const { data, error } = await this.userRepository.createUser(userData);

    if (error) {
      console.error("Error creating user:", error);
      throw new Error(error.message);
    }

    if (!data) {
      throw new Error("Nie udało się utworzyć użytkownika");
    }

    return data;
  }

  /**
   * Aktualizuje dane użytkownika
   * @param userId ID użytkownika
   * @param userData Dane do aktualizacji
   * @returns Zaktualizowany użytkownik
   */
  async updateUser(userId: string, userData: Partial<User>): Promise<User> {
    const { data, error } = await this.userRepository.updateUser(
      userId,
      userData
    );

    if (error) {
      console.error("Error updating user:", error);
      throw new Error(error.message);
    }

    if (!data) {
      throw new Error("Nie udało się zaktualizować użytkownika");
    }

    return data;
  }
}
