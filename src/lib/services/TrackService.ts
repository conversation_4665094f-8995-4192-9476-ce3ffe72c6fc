import type { TrackRepository } from "$lib/repositories/TrackRepository";
import type { SlugService } from "$lib/services/SlugService";
import type { Track } from "$lib/types/track";

export class TrackService {
  private trackRepository: TrackRepository;
  private slugService: SlugService;

  constructor(trackRepository: TrackRepository, slugService: SlugService) {
    this.trackRepository = trackRepository;
    this.slugService = slugService;
  }

  /**
   * Pobiera utwór po ID
   * @param trackId ID utworu
   * @returns Dane utworu
   */
  async getTrackById(trackId: number): Promise<Track> {
    const { data, error } = await this.trackRepository.getTrackById(trackId);

    if (error) {
      console.error("Error fetching track:", error);
      throw new Error(error.message);
    }

    if (!data) {
      throw new Error("Utwór nie został znaleziony");
    }

    return data;
  }

  /**
   * Tworzy nowy utwór
   * @param name Nazwa utworu
   * @param projectId ID projektu
   * @param uploadedBy ID użytkownika, który dodał utwór
   * @returns Utworzony utwór
   */
  async createTrack(
    name: string,
    projectId: number,
    uploadedBy: string
  ): Promise<Track> {
    if (!name || typeof name !== "string" || name.trim() === "") {
      throw new Error("Nazwa utworu nie może być pusta");
    }

    // Generowanie unikalnego sluga dla utworu
    const slug = await this.slugService.generateUniqueSlug("tracks", name);

    // Tworzenie utworu z wygenerowanym slugiem
    const { data, error } = await this.trackRepository.createTrack({
      name: name.trim(),
      project_id: projectId,
      uploaded_by: uploadedBy,
      slug,
    });

    if (error) {
      console.error("Error creating track:", error);
      throw new Error(error.message);
    }

    if (!data) {
      throw new Error("Nie udało się utworzyć utworu");
    }

    return data;
  }

  /**
   * Aktualizuje utwór
   * @param trackId ID utworu
   * @param name Nowa nazwa utworu
   * @returns Zaktualizowany utwór
   */
  async updateTrack(trackId: number, name: string): Promise<Track> {
    if (!name || typeof name !== "string" || name.trim() === "") {
      throw new Error("Nazwa utworu nie może być pusta");
    }

    // Pobierz aktualny utwór
    const { data: currentTrack, error: fetchError } =
      await this.trackRepository.getTrackById(trackId);

    if (fetchError) {
      console.error("Error fetching track:", fetchError);
      throw new Error(fetchError.message);
    }

    if (!currentTrack) {
      throw new Error("Utwór nie został znaleziony");
    }

    // Jeśli nazwa się nie zmieniła, zwróć aktualny utwór
    if (currentTrack.name === name.trim()) {
      return currentTrack;
    }

    // Generowanie nowego sluga, jeśli nazwa się zmieniła
    const slug = await this.slugService.generateUniqueSlug("tracks", name);

    // Aktualizacja utworu
    const { data, error } = await this.trackRepository.updateTrack(trackId, {
      name: name.trim(),
      slug,
    });

    if (error) {
      console.error("Error updating track:", error);
      throw new Error(error.message);
    }

    if (!data) {
      throw new Error("Nie udało się zaktualizować utworu");
    }

    return data;
  }
}
