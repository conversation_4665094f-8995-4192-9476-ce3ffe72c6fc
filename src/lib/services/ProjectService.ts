import type { ProjectRepository } from "$lib/repositories/ProjectRepository";
import type { SlugService } from "$lib/services/SlugService";
import type { Project } from "$lib/types/project";
import type { Track } from "$lib/types/track";
import type { User } from "$lib/types/user";

/**
 * Interfejs dla szczegółów projektu
 */
export interface ProjectDetails extends Project {
  members: User[];
  tracks: Track[];
}

export class ProjectService {
  private projectRepository: ProjectRepository;
  private slugService: SlugService;

  constructor(projectRepository: ProjectRepository, slugService: SlugService) {
    this.projectRepository = projectRepository;
    this.slugService = slugService;
  }

  /**
   * Pobiera wszystkie projekty użytkownika wraz z dodatkowymi danymi
   * @param userId ID użytkownika
   * @returns Lista projektów z dodatkowymi danymi
   */
  async getUserProjects(userId: string): Promise<Project[]> {
    // Pobierz projekty użytkownika
    const { data, error } = await this.projectRepository.getUserProjects(
      userId
    );

    if (error) {
      console.error("Error fetching projects:", error);
      throw new Error(error.message);
    }

    if (!data) {
      return [];
    }

    return data.map((project) => ({
      ...project,
      members: project.members?.map((m) => m.user) || [],
    }));
  }

  /**
   * Tworzy nowy projekt
   * @param name Nazwa projektu
   * @param userId ID użytkownika tworzącego projekt
   * @returns Utworzony projekt
   */
  async createProject(name: string, userId: string): Promise<Project> {
    if (!name || typeof name !== "string" || name.trim() === "") {
      throw new Error("Nazwa projektu nie może być pusta");
    }

    // Generowanie unikalnego sluga dla projektu
    const slug = await this.slugService.generateUniqueSlug("projects", name);

    // Tworzenie projektu z wygenerowanym slugiem
    const { data, error } = await this.projectRepository.createProject({
      name: name.trim(),
      slug,
    });

    if (error) {
      console.error("Error creating project:", error);
      throw new Error(error.message);
    }

    if (!data) {
      throw new Error("Nie udało się utworzyć projektu");
    }

    await this.projectRepository.addUserToProject(data.id, userId);

    return data;
  }

  /**
   * Aktualizuje projekt
   * @param projectId ID projektu
   * @param name Nowa nazwa projektu
   * @returns Zaktualizowany projekt
   */
  async updateProject(projectId: number, name: string): Promise<Project> {
    if (!name || typeof name !== "string" || name.trim() === "") {
      throw new Error("Nazwa projektu nie może być pusta");
    }

    // Pobierz aktualny projekt
    const { data: currentProject, error: fetchError } =
      await this.projectRepository.getProjectById(projectId);

    if (fetchError) {
      console.error("Error fetching project:", fetchError);
      throw new Error(fetchError.message);
    }

    if (!currentProject) {
      throw new Error("Projekt nie został znaleziony");
    }

    // Jeśli nazwa się nie zmieniła, zwróć aktualny projekt
    if (currentProject.name === name.trim()) {
      return currentProject;
    }

    // Generowanie nowego sluga, jeśli nazwa się zmieniła
    const slug = await this.slugService.generateUniqueSlug("projects", name);

    // Aktualizacja projektu
    const { data, error } = await this.projectRepository.updateProject(
      projectId,
      {
        name: name.trim(),
        slug,
        updated_at: new Date().toISOString(),
      }
    );

    if (error) {
      console.error("Error updating project:", error);
      throw new Error(error.message);
    }

    if (!data) {
      throw new Error("Nie udało się zaktualizować projektu");
    }

    return data;
  }

  /**
   * Pobiera szczegóły projektu wraz z członkami i utworami
   * @param projectId ID projektu
   * @param userId ID użytkownika żądającego dostępu
   * @returns Szczegóły projektu
   */
  async getProjectDetails(
    projectId: number,
    userId: string
  ): Promise<ProjectDetails> {
    // Sprawdź, czy użytkownik ma dostęp do projektu
    const { hasAccess, error: accessError } =
      await this.projectRepository.checkUserProjectAccess(projectId, userId);

    if (!hasAccess || accessError) {
      throw new Error("Nie masz dostępu do tego projektu");
    }

    // Pobierz szczegóły projektu
    const { data: project, error: projectError } =
      await this.projectRepository.getProjectById(projectId);

    if (projectError || !project) {
      throw new Error("Projekt nie został znaleziony");
    }

    // Pobierz członków projektu
    const { data: members, error: membersError } =
      await this.projectRepository.getProjectMembers(projectId);

    if (membersError) {
      console.error(
        `Error fetching members for project ${projectId}:`,
        membersError
      );
      throw new Error("Błąd podczas pobierania członków projektu");
    }

    // Pobierz utwory projektu
    const { data: tracks, error: tracksError } =
      await this.projectRepository.getAllProjectTracks(projectId);

    if (tracksError) {
      console.error(
        `Error fetching tracks for project ${projectId}:`,
        tracksError
      );
      throw new Error("Błąd podczas pobierania utworów projektu");
    }

    // Zwróć kompletne dane projektu
    return {
      ...project,
      members: members?.map((m) => m.user) || [],
      tracks: tracks || [],
    };
  }

  /**
   * Usuwa projekt
   * @param projectId ID projektu
   * @param userId ID użytkownika żądającego usunięcia
   * @returns Informacja o powodzeniu operacji
   */
  async deleteProject(
    projectId: number,
    userId: string
  ): Promise<{ success: boolean; message: string }> {
    // Sprawdź, czy użytkownik ma dostęp do projektu
    const { hasAccess, error: accessError } =
      await this.projectRepository.checkUserProjectAccess(projectId, userId);

    if (!hasAccess || accessError) {
      throw new Error("Nie masz dostępu do tego projektu");
    }

    // Pobierz projekt, aby uzyskać slug
    const { data: project, error: projectError } =
      await this.projectRepository.getProjectById(projectId);

    if (projectError || !project) {
      throw new Error("Projekt nie został znaleziony");
    }

    // Usuń projekt
    const { error: deleteError } = await this.projectRepository.deleteProject(
      projectId,
      project.slug
    );

    if (deleteError) {
      console.error("Błąd podczas usuwania projektu:", deleteError);
      throw new Error(deleteError.message);
    }

    return {
      success: true,
      message: "Projekt został pomyślnie usunięty",
    };
  }
}
