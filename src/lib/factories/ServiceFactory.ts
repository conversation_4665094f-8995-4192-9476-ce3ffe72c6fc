import type { Database } from "$lib/database.types";
import { ProjectRepository } from "$lib/repositories/ProjectRepository";
import { TrackRepository } from "$lib/repositories/TrackRepository";
import { UserRepository } from "$lib/repositories/UserRepository";
import { ProjectService } from "$lib/services/ProjectService";
import { SlugService } from "$lib/services/SlugService";
import { TrackService } from "$lib/services/TrackService";
import { UserService } from "$lib/services/UserService";
import type { SupabaseClient } from "@supabase/supabase-js";

/**
 * Fabryka serwisów aplikacji
 */
export class ServiceFactory {
  private static projectRepository: ProjectRepository | null = null;
  private static trackRepository: TrackRepository | null = null;
  private static userRepository: UserRepository | null = null;

  private static slugService: SlugService | null = null;
  private static projectService: ProjectService | null = null;
  private static trackService: TrackService | null = null;
  private static userService: UserService | null = null;

  /**
   * Inicjalizuje fabrykę serwisów
   * @param supabase Klient Supabase
   */
  static initialize(supabase: SupabaseClient<Database>): void {
    // Inicjalizacja repozytoriów
    this.projectRepository = new ProjectRepository(supabase);
    this.trackRepository = new TrackRepository(supabase);
    this.userRepository = new UserRepository(supabase);

    // Inicjalizacja serwisów pomocniczych
    this.slugService = new SlugService(supabase);

    // Inicjalizacja serwisów głównych
    this.projectService = new ProjectService(
      this.projectRepository,
      this.slugService
    );
    this.trackService = new TrackService(
      this.trackRepository,
      this.slugService
    );
    this.userService = new UserService(this.userRepository);
  }

  /**
   * Zwraca serwis projektów
   * @returns Serwis projektów
   */
  static getProjectService(): ProjectService {
    if (!this.projectService) {
      throw new Error(
        "ServiceFactory nie zostało zainicjalizowane. Wywołaj najpierw initialize()."
      );
    }
    return this.projectService;
  }

  /**
   * Zwraca serwis utworów
   * @returns Serwis utworów
   */
  static getTrackService(): TrackService {
    if (!this.trackService) {
      throw new Error(
        "ServiceFactory nie zostało zainicjalizowane. Wywołaj najpierw initialize()."
      );
    }
    return this.trackService;
  }

  /**
   * Zwraca serwis użytkowników
   * @returns Serwis użytkowników
   */
  static getUserService(): UserService {
    if (!this.userService) {
      throw new Error(
        "ServiceFactory nie zostało zainicjalizowane. Wywołaj najpierw initialize()."
      );
    }
    return this.userService;
  }

  /**
   * Zwraca serwis slugów
   * @returns Serwis slugów
   */
  static getSlugService(): SlugService {
    if (!this.slugService) {
      throw new Error(
        "ServiceFactory nie zostało zainicjalizowane. Wywołaj najpierw initialize()."
      );
    }
    return this.slugService;
  }
}
