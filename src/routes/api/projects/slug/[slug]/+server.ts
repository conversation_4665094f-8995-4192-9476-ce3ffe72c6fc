import { error, json } from "@sveltejs/kit";
import type { RequestHandler } from "./$types";

/**
 * Endpoint GET do pobierania szczegółów projektu na podstawie sluga.
 * Zwraca szczegółowe informacje o projekcie, jego członkach i utworach.
 */
export const GET: RequestHandler = async ({
  locals: { user, services, supabase },
  params,
}) => {
  // Sprawdź, czy użytkownik jest zalogowany
  if (!user) {
    throw error(401, { message: "Nieautoryzowany dostęp" });
  }

  const slug = params.slug;
  if (!slug) {
    throw error(400, { message: "Nieprawidłowy slug projektu" });
  }

  try {
    // Najpierw pobierz projekt na podstawie sluga, aby uzyskać ID
    const { data: project, error: projectError } = await supabase
      .from("projects")
      .select("id")
      .eq("slug", slug)
      .single();

    if (projectError || !project) {
      throw error(404, { message: "Projekt nie został znaleziony" });
    }

    // Pobierz szczegóły projektu za pomocą serwisu
    const projectDetails = await services.projectService.getProjectDetails(
      project.id,
      user.id
    );

    // Pobierz kategorie utworów
    const { data: categories, error: categoriesError } = await supabase
      .from("track_categories")
      .select();

    if (categoriesError) {
      console.error(
        `Error fetching categories for project ${slug}:`,
        categoriesError
      );
      throw error(500, { message: "Błąd podczas pobierania kategorii" });
    }

    // Przekształć dane utworów, dodając liczbę plików
    const transformedTracks = projectDetails.tracks.map((track) => {
      // Pobierz liczbę plików dla utworu
      const filesCount = track.files_count || 0;
      return {
        ...track,
        files_count: filesCount,
      };
    });

    // Zwróć kompletne dane projektu
    return json({
      project: projectDetails,
      tracks: transformedTracks,
      categories,
      projectUsers: projectDetails.members,
    });
  } catch (err) {
    console.error(`Error in GET /api/projects/slug/${slug}:`, err);
    
    if (err instanceof Error) {
      if (err.message === "Nie masz dostępu do tego projektu") {
        throw error(403, { message: err.message });
      } else if (err.message === "Projekt nie został znaleziony") {
        throw error(404, { message: err.message });
      } else if (
        err.message === "Błąd podczas pobierania członków projektu" ||
        err.message === "Błąd podczas pobierania utworów projektu"
      ) {
        throw error(500, { message: err.message });
      }
    }
    
    throw error(500, { message: "Wystąpił błąd podczas pobierania projektu" });
  }
};
