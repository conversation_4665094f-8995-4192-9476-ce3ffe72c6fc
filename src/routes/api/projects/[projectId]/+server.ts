import { error, json } from "@sveltejs/kit";
import type { RequestHandler } from "./$types";

/**
 * Endpoint GET do pobierania szczegółów projektu.
 * Zwraca szczegółowe informacje o projekcie, jego członkach i utworach.
 */
export const GET: RequestHandler = async ({
  locals: { user, services },
  params,
}) => {
  // Sprawdź, czy użytkownik jest zalogowany
  if (!user) {
    throw error(401, { message: "Nieautoryzowany dostęp" });
  }

  const projectId = params.projectId;
  if (!projectId || isNaN(parseInt(projectId))) {
    throw error(400, { message: "Nieprawidłowe ID projektu" });
  }

  try {
    // Pobierz szczegóły projektu za pomocą serwisu
    const projectDetails = await services.projectService.getProjectDetails(
      parseInt(projectId),
      user.id
    );

    return json(projectDetails);
  } catch (err) {
    console.error(`Error in GET /api/projects/${projectId}:`, err);

    if (err instanceof Error) {
      if (err.message === "Nie masz dostępu do tego projektu") {
        throw error(403, { message: err.message });
      } else if (err.message === "Projekt nie został znaleziony") {
        throw error(404, { message: err.message });
      } else if (
        err.message === "Błąd podczas pobierania członków projektu" ||
        err.message === "Błąd podczas pobierania utworów projektu"
      ) {
        throw error(500, { message: err.message });
      }
    }

    throw error(500, { message: "Wystąpił błąd podczas pobierania projektu" });
  }
};

/**
 * Endpoint PATCH do aktualizacji projektu.
 * Obsługuje żądanie PATCH z nową nazwą projektu.
 */
export const PATCH: RequestHandler = async ({ request, locals, params }) => {
  // Sprawdź czy użytkownik jest zalogowany
  const { user, services } = locals;

  if (!user) {
    throw error(401, { message: "Nie jesteś zalogowany" });
  }

  const projectId = params.projectId;
  if (!projectId || isNaN(parseInt(projectId))) {
    throw error(400, { message: "Nieprawidłowe ID projektu" });
  }

  // Pobierz dane z żądania
  let body;
  try {
    body = await request.json();
  } catch (e) {
    throw error(400, { message: "Nieprawidłowy format danych" });
  }

  // Walidacja danych
  const { name } = body;

  try {
    // Aktualizacja projektu za pomocą serwisu
    const updatedProject = await services.projectService.updateProject(
      parseInt(projectId),
      name
    );

    // Zwróć zaktualizowane dane projektu
    return json({
      success: true,
      project: updatedProject,
    });
  } catch (err) {
    console.error("Błąd podczas aktualizacji projektu:", err);

    if (err instanceof Error) {
      if (err.message === "Nazwa projektu nie może być pusta") {
        throw error(400, { message: err.message });
      } else if (err.message === "Nie masz dostępu do tego projektu") {
        throw error(403, { message: err.message });
      } else if (err.message === "Projekt nie został znaleziony") {
        throw error(404, { message: err.message });
      }
    }

    throw error(500, {
      message: "Wystąpił błąd podczas aktualizacji projektu",
    });
  }
};
export const DELETE: RequestHandler = async ({
  locals: { user, services },
  params,
}) => {
  // Sprawdź, czy użytkownik jest zalogowany
  if (!user) {
    throw error(401, { message: "Nieautoryzowany dostęp" });
  }

  const projectId = params.projectId;
  if (!projectId || isNaN(parseInt(projectId))) {
    throw error(400, { message: "Nieprawidłowe ID projektu" });
  }

  try {
    // Usuń projekt za pomocą serwisu
    const result = await services.projectService.deleteProject(
      parseInt(projectId),
      user.id
    );

    // Zwróć informację o powodzeniu operacji
    return json(result);
  } catch (err) {
    console.error(`Error in DELETE /api/projects/${projectId}:`, err);

    if (err instanceof Error) {
      if (err.message === "Nie masz dostępu do tego projektu") {
        throw error(403, { message: err.message });
      } else if (err.message === "Projekt nie został znaleziony") {
        throw error(404, { message: err.message });
      }
    }

    throw error(500, { message: "Wystąpił błąd podczas usuwania projektu" });
  }
};
