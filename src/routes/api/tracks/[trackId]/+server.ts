import { error, json, type RequestHandler } from "@sveltejs/kit";

export const GET: RequestHandler = async ({
  request,
  locals: { supabase, user },
  params,
}) => {
  const trackId = parseInt(params.trackId ?? "");

  const { data: track, error: trackError } = await supabase
    .from("tracks")
    .select("*")
    .eq("id", trackId)
    .single();

  if (!track || trackError) {
    throw error(404, {
      message: trackError?.message || "Utwór nie został znaleziony",
    });
  }

  return json(track);
};

/**
 * Endpoint DELETE do usuwania utworu.
 * Usuwa utwór wraz z powiązanymi plikami.
 */
export const DELETE: RequestHandler = async ({
  locals: { supabase, user },
  params,
}) => {
  // Sprawdź, czy użytkownik jest zalogowany
  if (!user) {
    throw error(401, { message: "Nieautoryzowany dostęp" });
  }

  const trackId = parseInt(params.trackId ?? "");
  if (isNaN(trackId)) {
    throw error(400, { message: "Nieprawidłowe ID utworu" });
  }

  try {
    // Pobierz informacje o utworze
    const { data: track, error: trackError } = await supabase
      .from("tracks")
      .select("*, project:project_id(id)")
      .eq("id", trackId)
      .single();

    if (trackError || !track) {
      throw error(404, { message: "Utwór nie został znaleziony" });
    }

    // Sprawdź, czy użytkownik ma dostęp do projektu, do którego należy utwór
    const { data: projectAccess, error: accessError } = await supabase
      .from("projects_users")
      .select("*")
      .eq("project_id", track.project.id)
      .eq("user_id", user.id)
      .single();

    if (accessError || !projectAccess) {
      throw error(403, { message: "Nie masz dostępu do tego projektu" });
    }

    // Usuń plik z storage, jeśli istnieje
    if (track.storage_file_path) {
      const { error: storageError } = await supabase.storage
        .from("project_files")
        .remove([track.storage_file_path]);

      if (storageError) {
        console.error("Błąd podczas usuwania pliku z storage:", storageError);
        throw error(500, { message: storageError.message });
      }
    }

    // Usuń utwór z bazy danych
    const { error: deleteError } = await supabase
      .from("tracks")
      .delete()
      .eq("id", trackId);

    if (deleteError) {
      console.error("Błąd podczas usuwania utworu:", deleteError);
      throw error(500, { message: deleteError.message });
    }

    // Zwróć informację o powodzeniu operacji
    return json({
      success: true,
      message: "Utwór został pomyślnie usunięty",
    });
  } catch (err) {
    console.error(`Error in DELETE /api/tracks/${trackId}:`, err);

    if (err instanceof Error) {
      throw error(500, { message: err.message });
    }

    throw error(500, { message: "Wystąpił błąd podczas usuwania utworu" });
  }
};
