<script lang="ts">
  import DeleteProjectModal from "$lib/components/projects/DeleteProjectModal.svelte";
  import EditProjectModal from "$lib/components/projects/EditProjectModal.svelte";
  import LeaveProjectModal from "$lib/components/projects/LeaveProjectModal.svelte";
  import ProjectInviteModal from "$lib/components/projects/ProjectInviteModal.svelte";
  import ProjectMembersModal from "$lib/components/projects/ProjectMembersModal.svelte";
  import DeleteTrackModal from "$lib/components/tracks/DeleteTrackModal.svelte";
  import NewTrackModal from "$lib/components/tracks/NewTrackModal.svelte";

  import ProjectActionButton from "$lib/components/projects/ProjectActionButton.svelte";
  import TrackList from "$lib/components/tracks/TrackList.svelte";
  import Button from "$lib/components/ui/Button.svelte";
  import LoadingSpinner from "$lib/components/ui/LoadingSpinner.svelte";
  import { setSupabaseContext } from "$lib/supabase-context";

  import type { Project } from "$lib/types/project";
  import type { Track } from "$lib/types/track";
  import type { TrackCategory } from "$lib/types/track_category";
  import type { User } from "$lib/types/user";
  import { Edit, LogOut, Share2, Trash2, Users } from "lucide-svelte";
  import { onMount } from "svelte";

  // Pobierz dane z layoutu
  const { data } = $props();

  // Ustaw kontekst Supabase
  setSupabaseContext(data.supabase);

  // Zmienne stanu dla danych projektu
  let project = $state<Project | null>(null);
  let tracks = $state<Track[]>([]);
  let projectUsers = $state<User[]>([]);
  let categories = $state<TrackCategory[]>([]);

  // Zmienne stanu dla UI
  let isLoading = $state(true);
  let error = $state<string | null>(null);
  let trackToDelete: Track | null = $state(null);
  let isDeleteTrackModalOpen = $state(false);
  let isLeaveModalOpen = $state(false);
  let isDeleteProjectModalOpen = $state(false);
  let isUsersModalOpen = $state(false);
  let isEditProjectModalOpen = $state(false);
  let isCreateModalOpen = $state(false);
  let isInviteModalOpen = $state(false);

  // Pobierz slug projektu z URL
  const projectSlug = window.location.pathname.split("/")[1];

  // Funkcja do pobierania danych projektu
  async function fetchProjectData() {
    isLoading = true;
    error = null;

    try {
      const response = await fetch(`/api/projects/slug/${projectSlug}`);

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message ||
            "Wystąpił błąd podczas pobierania danych projektu"
        );
      }

      const data = await response.json();

      // Przypisz dane do zmiennych stanu
      project = data.project;
      tracks = data.tracks;
      categories = data.categories;
      projectUsers = data.projectUsers;

      // Nie ustawiamy już kontekstu Supabase, ponieważ będziemy korzystać z API
    } catch (err) {
      console.error("Błąd podczas pobierania danych projektu:", err);
      error = err instanceof Error ? err.message : "Wystąpił nieznany błąd";
    } finally {
      isLoading = false;
    }
  }

  function openCreateModal() {
    isCreateModalOpen = true;
  }

  // Pobierz dane przy montowaniu komponentu
  onMount(() => {
    fetchProjectData();
  });
</script>

<div class="h-full scrollbar-stable">
  <div class="p-6 sm:p-8 max-w-7xl mx-auto">
    <!-- Nagłówek zawsze widoczny -->
    <div
      class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6 sm:mb-10"
    >
      <div>
        <div class="flex items-center gap-2">
          <a href="/dashboard" class="text-gray-400 hover:text-white"
            >Projekty</a
          >
          <span class="text-gray-500">›</span>
          <span class="text-white font-medium flex items-center gap-2">
            {#if project}
              {project.name}
            {:else}
              <LoadingSpinner size={16} thickness={1.5} />
              <span class="text-gray-400">Wczytywanie projektu</span>
            {/if}
          </span>
        </div>
      </div>

      <div class="flex flex-wrap gap-2 w-full sm:w-auto justify-end">
        <ProjectActionButton
          icon={Edit}
          onclick={() => project && (isEditProjectModalOpen = true)}
          disabled={!project}
        >
          <span class="sm:hidden md:inline">Edytuj</span>
          <span class="hidden sm:inline md:hidden">Edytuj</span>
        </ProjectActionButton>
        <ProjectActionButton
          icon={Share2}
          onclick={() => project && (isInviteModalOpen = true)}
          disabled={!project}
        >
          <span class="sm:hidden md:inline">Udostępnij</span>
          <span class="hidden sm:inline md:hidden">Udostępnij</span>
        </ProjectActionButton>
        <ProjectActionButton
          icon={Users}
          onclick={() => project && (isUsersModalOpen = true)}
          disabled={!project}
        >
          <span class="sm:hidden md:inline">Członkowie</span>
          <span class="hidden sm:inline md:hidden">Członkowie</span>
        </ProjectActionButton>
        <ProjectActionButton
          icon={LogOut}
          onclick={() => project && (isLeaveModalOpen = true)}
          disabled={!project}
        >
          <span class="sm:hidden md:inline">Opuść</span>
          <span class="hidden sm:inline md:hidden">Opuść</span>
        </ProjectActionButton>
        <ProjectActionButton
          icon={Trash2}
          onclick={() => project && (isDeleteProjectModalOpen = true)}
          disabled={!project}
        >
          <span class="sm:hidden md:inline">Usuń</span>
          <span class="hidden sm:inline md:hidden">Usuń</span>
        </ProjectActionButton>
      </div>
    </div>

    <!-- Zawartość zależna od stanu ładowania -->
    {#if isLoading}
      <div class="h-64 w-full flex items-center justify-center">
        <LoadingSpinner size={40} />
      </div>
    {:else if error}
      <div class="h-64 flex flex-col items-center justify-center text-center">
        <p class="text-red-500 mb-4">
          Wystąpił błąd podczas ładowania danych projektu:
        </p>
        <p class="mb-4">{error}</p>
        <Button onclick={() => fetchProjectData()}>Spróbuj ponownie</Button>
      </div>
    {:else if project}
      <TrackList
        {tracks}
        onNewTrack={openCreateModal}
        onDeleteTrack={(track) => {
          trackToDelete = track;
          isDeleteTrackModalOpen = true;
        }}
      />
    {/if}
  </div>
</div>

{#if project}
  <ProjectInviteModal {project} bind:isOpen={isInviteModalOpen} />
  <ProjectMembersModal bind:isOpen={isUsersModalOpen} {projectUsers} />
  <LeaveProjectModal {project} bind:isOpen={isLeaveModalOpen} />
  <DeleteProjectModal {project} bind:isOpen={isDeleteProjectModalOpen} />
  <EditProjectModal
    {project}
    bind:isOpen={isEditProjectModalOpen}
    onProjectUpdated={(updatedProject) => {
      if (updatedProject) {
        // Jeśli otrzymaliśmy zaktualizowany projekt, aktualizujemy dane bez pobierania z API
        project = updatedProject;
      } else {
        // W przeciwnym razie pobieramy dane z API
        fetchProjectData();
      }
    }}
  />
  <DeleteTrackModal
    track={trackToDelete}
    bind:isOpen={isDeleteTrackModalOpen}
    onClose={() => {
      isDeleteTrackModalOpen = false;
      trackToDelete = null;
      fetchProjectData(); // Odświeżamy dane zamiast przeładowywać stronę
    }}
  />
  <NewTrackModal bind:isOpen={isCreateModalOpen} {project} {categories} />
{/if}
