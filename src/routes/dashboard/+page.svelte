<script lang="ts">
  import NewProjectModal from "$lib/components/projects/NewProjectModal.svelte";
  import NoProjectsView from "$lib/components/projects/NoProjectsView.svelte";
  import ProjectCard from "$lib/components/projects/ProjectCard.svelte";
  import Button from "$lib/components/ui/Button.svelte";
  import LoadingSpinner from "$lib/components/ui/LoadingSpinner.svelte";
  import type { DashboardProject } from "$lib/types/project";
  import { Plus } from "lucide-svelte";
  import { onMount } from "svelte";

  let projects = $state<DashboardProject[]>([]);
  let isLoading = $state(true);
  let error = $state<string | null>(null);
  let isCreateProjectModalOpened = $state(false);

  const hasProjects = $derived(projects.length > 0);

  async function fetchProjects() {
    isLoading = true;
    error = null;

    try {
      const response = await fetch("/api/projects");

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          errorData.message || "Wystąpił błąd podczas pobierania projektów"
        );
      }

      projects = await response.json();
    } catch (err) {
      console.error("Błąd podczas pobierania projektów:", err);
      error = err instanceof Error ? err.message : "Wystąpił nieznany błąd";
    } finally {
      isLoading = false;
    }
  }

  onMount(() => {
    fetchProjects();
  });
</script>

<div class="p-4 sm:p-6 md:p-8 max-w-7xl mx-auto">
  <!-- Nagłówek zawsze widoczny, chyba że nie ma projektów i nie trwa ładowanie -->
  {#if !(!hasProjects && !isLoading)}
    <div
      class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6"
    >
      <div>
        <h1 class="text-2xl font-bold">Moje Projekty</h1>
        <p class="text-gray-400 mt-1">
          Zarządzaj i organizuj swoje projekty muzyczne
        </p>
      </div>

      <Button
        onclick={() => (isCreateProjectModalOpened = true)}
        primary
        class="w-full sm:w-auto"
      >
        <Plus size={20} />
        <span>Nowy Projekt</span>
      </Button>
    </div>
  {/if}

  {#if isLoading}
    <div class="h-64 w-full flex items-center justify-center">
      <LoadingSpinner size={40} />
    </div>
  {:else if error}
    <div class="h-64 flex flex-col items-center justify-center text-center">
      <p class="text-red-500 mb-4">
        Wystąpił błąd podczas ładowania projektów:
      </p>
      <p class="mb-4">{error}</p>
      <Button onclick={() => fetchProjects()}>Spróbuj ponownie</Button>
    </div>
  {:else if hasProjects}
    <div
      class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 sm:gap-6"
    >
      {#each projects as project (project.id)}
        <ProjectCard {project} />
      {/each}
    </div>
  {:else}
    <div class="h-full flex items-center justify-center">
      <NoProjectsView
        onCreateProject={() => (isCreateProjectModalOpened = true)}
      />
    </div>
  {/if}
</div>

<NewProjectModal bind:isOpen={isCreateProjectModalOpened} />
