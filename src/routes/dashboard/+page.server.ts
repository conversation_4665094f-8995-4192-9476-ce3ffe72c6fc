import { error, redirect, type Actions } from "@sveltejs/kit";
import type { PageServerLoad } from "./$types";

export const load: PageServerLoad = async ({ locals: { session } }) => {
  // Sprawdzamy, czy użytkownik jest zalogowany
  if (!session) {
    redirect(303, "/");
  }

  // Nie pobieramy już projektów w load - będą pobierane przez endpoint API
  return {};
};

export const actions = {
  delete: async ({ request, locals: { supabase, session } }) => {
    // Sprawdzamy czy użytkownik jest zalogowany
    if (!session) {
      return error(401, {
        message: "Mu<PERSON><PERSON> być zalogowany, aby usunąć projekt",
      });
    }

    const formData = await request.formData();
    const id = formData.get("id")?.toString();
    const slug = formData.get("slug")?.toString();
    if (!id || !slug) {
      throw error(400, { message: "Project id and slug are required" });
    }

    // 1. Pobierz listę wszystkich plików w storage
    const { data: filesList, error: listError } = await supabase.storage
      .from("project_files")
      .list(slug);

    if (listError) {
      console.error("Błąd podczas pobierania listy plików:", listError);
      throw error(500, { message: listError.message });
    }

    // 2. Utwórz tablicę ścieżek do plików
    const filePaths = filesList.map((file) => `${slug}/${file.name}`);

    // 3. Usuń wszystkie pliki jednocześnie
    if (filePaths.length > 0) {
      const { error: storageError } = await supabase.storage
        .from("project_files")
        .remove(filePaths);

      if (storageError) {
        console.error("Błąd podczas usuwania plików:", storageError);
        throw error(500, { message: storageError.message });
      }
    }

    // 2. usun rekord z bazy danych
    const { error: dbError } = await supabase
      .from("projects")
      .delete()
      .eq("id", parseInt(id));
    if (dbError) {
      console.error("Error deleting project:", dbError);
      throw error(500, { message: dbError.message });
    }

    return { success: true };
  },
} satisfies Actions;
