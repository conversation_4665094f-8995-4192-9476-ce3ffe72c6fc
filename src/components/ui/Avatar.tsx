import React from 'react';

interface AvatarProps {
  src: string;
  alt: string;
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl';
  className?: string;
}

export function Avatar({ src, alt, size = 'md', className = '' }: AvatarProps) {
  const sizeClasses = {
    xs: 'h-6 w-6',
    sm: 'h-8 w-8',
    md: 'h-10 w-10',
    lg: 'h-12 w-12',
    xl: 'h-16 w-16'
  };
  
  return (
    <div 
      className={`
        ${sizeClasses[size]} 
        rounded-full overflow-hidden bg-gray-700 
        flex items-center justify-center
        ${className}
      `}
    >
      {src ? (
        <img 
          src={src} 
          alt={alt} 
          className="h-full w-full object-cover"
          onError={(e) => {
            e.currentTarget.src = `https://ui-avatars.com/api/?name=${encodeURIComponent(alt)}&background=3B82F6&color=fff`;
          }}
        />
      ) : (
        <span className="text-white font-medium">
          {alt.split(' ').map(name => name[0]).join('').toUpperCase()}
        </span>
      )}
    </div>
  );
}