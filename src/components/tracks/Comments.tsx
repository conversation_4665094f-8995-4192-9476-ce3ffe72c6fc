import React, { useState, useRef, useEffect } from 'react';
import { Avatar } from '../ui/Avatar';
import { TextArea } from '../ui/Input';
import { Button } from '../ui/Button';
import { formatDistanceToNow } from '../../utils/dateUtils';
import { Comment } from '../../types';
import { currentUser } from '../../data/mockData';
import { Send, MessageSquare, ChevronRight, ChevronLeft } from 'lucide-react';

interface CommentsProps {
  comments: Comment[];
  onAddComment: (text: string) => void;
  isMobileView?: boolean;
  onToggleVisibility?: () => void;
}

export function Comments({ 
  comments, 
  onAddComment, 
  isMobileView = false,
  onToggleVisibility
}: CommentsProps) {
  const [commentText, setCommentText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const commentsEndRef = useRef<HTMLDivElement>(null);
  
  // Scroll to bottom when new comments are added
  useEffect(() => {
    commentsEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [comments]);
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!commentText.trim()) return;
    
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      onAddComment(commentText);
      setCommentText('');
      setIsLoading(false);
    }, 500);
  };
  
  return (
    <div className="h-full flex flex-col">
      <div className="flex items-center justify-between border-b border-gray-700 p-4">
        <div className="flex items-center">
          <MessageSquare size={18} className="text-gray-400 mr-2" />
          <h3 className="text-lg font-medium text-white">
            Comments {comments.length > 0 && <span className="text-sm text-gray-400">({comments.length})</span>}
          </h3>
        </div>
        
        {isMobileView && onToggleVisibility && (
          <button
            onClick={onToggleVisibility}
            className="text-gray-400 hover:text-white ml-4"
          >
            <ChevronRight size={20} />
          </button>
        )}
      </div>
      
      {comments.length === 0 ? (
        <div className="flex-1 flex items-center justify-center p-4">
          <div className="text-center">
            <MessageSquare size={24} className="text-gray-500 mx-auto mb-3" />
            <p className="text-gray-400 text-sm">No comments yet</p>
            <p className="text-gray-500 text-xs mt-1">Be the first to leave feedback</p>
          </div>
        </div>
      ) : (
        <div className="flex-1 overflow-y-auto p-4 space-y-6">
          {comments.map((comment) => (
            <div key={comment.id} className="flex">
              <Avatar 
                src={comment.user.avatar} 
                alt={comment.user.name} 
                size="sm"
                className="mr-3 flex-shrink-0"
              />
              <div className="flex-1">
                <div className="flex items-baseline">
                  <span className="text-sm font-medium text-white">{comment.user.name}</span>
                  <span className="ml-2 text-xs text-gray-500">
                    {formatDistanceToNow(new Date(comment.createdAt))}
                  </span>
                </div>
                <p className="mt-1 text-sm text-gray-300">{comment.text}</p>
              </div>
            </div>
          ))}
          <div ref={commentsEndRef} />
        </div>
      )}
      
      <div className="border-t border-gray-700 p-4">
        <form onSubmit={handleSubmit}>
          <div className="flex items-start">
            <Avatar 
              src={currentUser.avatar} 
              alt={currentUser.name} 
              size="sm"
              className="mr-3 flex-shrink-0 mt-1"
            />
            <div className="flex-1">
              <TextArea
                placeholder="Add a comment..."
                value={commentText}
                onChange={(e) => setCommentText(e.target.value)}
                className="min-h-[80px] resize-none"
              />
              <div className="flex justify-end mt-2">
                <Button
                  type="submit"
                  disabled={!commentText.trim()}
                  isLoading={isLoading}
                  size="sm"
                  icon={<Send size={16} />}
                >
                  Send
                </Button>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}

export function MobileCommentsToggle({ onClick, isVisible }: { onClick: () => void, isVisible: boolean }) {
  return (
    <button
      onClick={onClick}
      className="fixed bottom-4 right-4 z-10 bg-blue-600 hover:bg-blue-700 text-white rounded-full p-3 shadow-lg transition-colors"
    >
      {isVisible ? <ChevronRight size={20} /> : <MessageSquare size={20} />}
    </button>
  );
}