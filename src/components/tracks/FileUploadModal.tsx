import React, { useState, useRef } from 'react';
import { Modal } from '../ui/Modal';
import { Button } from '../ui/Button';
import { Dropdown } from '../ui/Dropdown';
import { Upload, File, Music, Mic, Drum, Guitar } from 'lucide-react';
import { FileCategory } from '../../types';

interface FileUploadModalProps {
  isOpen: boolean;
  onClose: () => void;
  onUpload: (file: File, category: FileCategory) => void;
  isLoading?: boolean;
}

export function FileUploadModal({ 
  isOpen, 
  onClose, 
  onUpload, 
  isLoading = false 
}: FileUploadModalProps) {
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [category, setCategory] = useState<FileCategory>('Vocals');
  const fileInputRef = useRef<HTMLInputElement>(null);
  
  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files.length > 0) {
      setSelectedFile(e.target.files[0]);
    }
  };
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (selectedFile) {
      onUpload(selectedFile, category);
      setSelectedFile(null);
      setCategory('Vocals');
    }
  };
  
  const categoryOptions = [
    { 
      value: 'Vocals', 
      label: 'Vocals',
      icon: <Mic size={16} className="text-pink-400" />
    },
    { 
      value: 'Drums', 
      label: 'Drums',
      icon: <Drum size={16} className="text-red-400" />
    },
    { 
      value: 'Bass', 
      label: 'Bass',
      icon: <Music size={16} className="text-purple-400" />
    },
    { 
      value: 'Guitar', 
      label: 'Guitar',
      icon: <Guitar size={16} className="text-yellow-400" />
    },
    { 
      value: 'Keys', 
      label: 'Keys',
      icon: <Music size={16} className="text-green-400" />
    },
    { 
      value: 'Other', 
      label: 'Other',
      icon: <File size={16} className="text-blue-400" />
    }
  ];
  
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Upload File"
      size="md"
    >
      <form onSubmit={handleSubmit}>
        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-300 mb-1">
            File
          </label>
          
          {selectedFile ? (
            <div className="bg-gray-800 border border-gray-700 rounded-md p-4">
              <div className="flex items-center">
                <File size={24} className="text-blue-500 mr-3" />
                <div className="flex-1 overflow-hidden">
                  <p className="text-sm text-white truncate">{selectedFile.name}</p>
                  <p className="text-xs text-gray-400">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
                <button
                  type="button"
                  className="text-sm text-red-500 hover:text-red-400"
                  onClick={() => setSelectedFile(null)}
                >
                  Remove
                </button>
              </div>
            </div>
          ) : (
            <div
              className="border-2 border-dashed border-gray-700 rounded-md p-6 text-center cursor-pointer hover:border-blue-500 transition-colors"
              onClick={() => fileInputRef.current?.click()}
            >
              <input
                type="file"
                className="hidden"
                ref={fileInputRef}
                onChange={handleFileChange}
                accept="audio/*"
              />
              <Upload size={24} className="mx-auto text-gray-400 mb-2" />
              <p className="text-sm text-white mb-1">Drop your audio file here, or click to browse</p>
              <p className="text-xs text-gray-400">Supported formats: MP3, WAV, AIFF (Max 50MB)</p>
            </div>
          )}
        </div>
        
        <Dropdown
          label="Category"
          options={categoryOptions}
          value={category}
          onChange={(value) => setCategory(value as FileCategory)}
        />
        
        <div className="flex justify-end space-x-3 mt-6">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            isLoading={isLoading}
            disabled={!selectedFile}
          >
            Upload File
          </Button>
        </div>
      </form>
    </Modal>
  );
}