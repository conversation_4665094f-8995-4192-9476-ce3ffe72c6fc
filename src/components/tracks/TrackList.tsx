import React from 'react';
import { formatDistanceToNow } from '../../utils/dateUtils';
import { Track } from '../../types';
import { Button } from '../ui/Button';
import { MoreVertical, Headphones, Plus, Music } from 'lucide-react';

interface TrackListProps {
  tracks: Track[];
  onTrackClick: (trackId: string) => void;
  onNewTrack: () => void;
}

export function TrackList({ tracks, onTrackClick, onNewTrack }: TrackListProps) {
  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-medium text-white">Tracks</h2>
        <Button 
          size="sm" 
          icon={<Plus size={16} />}
          onClick={onNewTrack}
        >
          New Track
        </Button>
      </div>
      
      {tracks.length === 0 ? (
        <div className="bg-gray-800 border-2 border-dashed border-gray-700 rounded-lg p-6 text-center">
          <div className="mx-auto w-16 h-16 bg-gray-700/50 rounded-full flex items-center justify-center mb-3">
            <Music size={24} className="text-gray-400" />
          </div>
          <h3 className="text-white font-medium mb-2">No tracks yet</h3>
          <p className="text-gray-400 text-sm mb-4">
            Start creating by adding your first track to this project
          </p>
          <Button 
            size="sm" 
            icon={<Plus size={16} />}
            onClick={onNewTrack}
          >
            Add Track
          </Button>
        </div>
      ) : (
        <div className="bg-gray-800 rounded-lg overflow-hidden">
          <table className="min-w-full divide-y divide-gray-700">
            <thead className="bg-gray-700/30">
              <tr>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider">
                  Name
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider hidden sm:table-cell">
                  Category
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider hidden md:table-cell">
                  Created
                </th>
                <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-400 uppercase tracking-wider hidden md:table-cell">
                  Files
                </th>
                <th scope="col" className="relative px-6 py-3">
                  <span className="sr-only">Actions</span>
                </th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-700">
              {tracks.map((track) => (
                <tr 
                  key={track.id} 
                  className="hover:bg-gray-700/30 cursor-pointer transition-colors"
                  onClick={() => onTrackClick(track.id)}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <Headphones size={16} className="mr-2 text-gray-400" />
                      <div className="text-sm font-medium text-white">{track.name}</div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400 hidden sm:table-cell">
                    {track.category}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400 hidden md:table-cell">
                    {formatDistanceToNow(new Date(track.createdAt))}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-400 hidden md:table-cell">
                    {track.files.length || 0}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button 
                      className="text-gray-400 hover:text-white" 
                      onClick={(e) => {
                        e.stopPropagation();
                        // Open track actions menu
                      }}
                    >
                      <MoreVertical size={16} />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      )}
    </div>
  );
}