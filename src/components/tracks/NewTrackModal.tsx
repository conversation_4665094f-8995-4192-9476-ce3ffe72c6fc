import React, { useState } from 'react';
import { Modal } from '../ui/Modal';
import { Input } from '../ui/Input';
import { Button } from '../ui/Button';
import { Dropdown } from '../ui/Dropdown';

interface NewTrackModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateTrack: (name: string, category: string) => void;
  isLoading?: boolean;
}

export function NewTrackModal({ 
  isOpen, 
  onClose, 
  onCreateTrack, 
  isLoading = false 
}: NewTrackModalProps) {
  const [trackName, setTrackName] = useState('');
  const [trackCategory, setTrackCategory] = useState('Song');
  const [error, setError] = useState('');
  
  const categoryOptions = [
    { value: 'Song', label: 'Song' },
    { value: 'Instrumental', label: 'Instrumental' },
    { value: 'Demo', label: 'Demo' },
    { value: 'Remix', label: 'Remix' },
    { value: 'Cover', label: 'Cover' },
  ];
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!trackName.trim()) {
      setError('Track name is required');
      return;
    }
    
    onCreateTrack(trackName, trackCategory);
    setTrackName('');
    setTrackCategory('Song');
    setError('');
  };
  
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Create New Track"
      size="sm"
    >
      <form onSubmit={handleSubmit}>
        <Input
          label="Track Name"
          placeholder="Enter track name"
          value={trackName}
          onChange={(e) => {
            setTrackName(e.target.value);
            if (error) setError('');
          }}
          error={error}
          autoFocus
        />
        
        <Dropdown
          label="Category"
          options={categoryOptions}
          value={trackCategory}
          onChange={setTrackCategory}
        />
        
        <div className="flex justify-end space-x-3 mt-6">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            isLoading={isLoading}
          >
            Create Track
          </Button>
        </div>
      </form>
    </Modal>
  );
}