import React, { useState } from 'react';
import { formatDistanceToNow } from '../../utils/dateUtils';
import { TrackFile, FileCategory } from '../../types';
import { Button } from '../ui/Button';
import { Dropdown } from '../ui/Dropdown';
import { File, Music, Mic, Drum, Guitar, Star, MoreVertical, Plus, Check } from 'lucide-react';

interface TrackFilesProps {
  files: TrackFile[];
  onFileSelect: (fileId: string) => void;
  onAddFile: () => void;
  selectedFileId: string | null;
}

export function TrackFiles({ files, onFileSelect, onAddFile, selectedFileId }: TrackFilesProps) {
  const [filter, setFilter] = useState<string>('all');
  
  const filterOptions = [
    { value: 'all', label: 'All Files' },
    { 
      value: 'Vocals', 
      label: 'Vocals',
      icon: <Mic size={16} className="text-pink-400" />
    },
    { 
      value: 'Drums', 
      label: 'Drums',
      icon: <Drum size={16} className="text-red-400" />
    },
    { 
      value: 'Bass', 
      label: 'Bass',
      icon: <Music size={16} className="text-purple-400" />
    },
    { 
      value: 'Guitar', 
      label: 'Guitar',
      icon: <Guitar size={16} className="text-yellow-400" />
    },
    { 
      value: 'Keys', 
      label: 'Keys',
      icon: <Music size={16} className="text-green-400" />
    },
    { 
      value: 'Other', 
      label: 'Other',
      icon: <File size={16} className="text-blue-400" />
    }
  ];
  
  const filteredFiles = filter === 'all' 
    ? files 
    : files.filter(file => file.category === filter);
  
  // Group files by category
  const groupedFiles: Record<FileCategory, TrackFile[]> = {
    'Vocals': [],
    'Drums': [],
    'Bass': [],
    'Guitar': [],
    'Keys': [],
    'Other': []
  };
  
  filteredFiles.forEach(file => {
    if (file.category in groupedFiles) {
      groupedFiles[file.category as FileCategory].push(file);
    } else {
      groupedFiles['Other'].push(file);
    }
  });
  
  // Get category icon
  const getCategoryIcon = (category: string) => {
    switch(category) {
      case 'Vocals': return <Mic size={18} className="text-pink-400" />;
      case 'Drums': return <Drum size={18} className="text-red-400" />;
      case 'Bass': return <Music size={18} className="text-purple-400" />;
      case 'Guitar': return <Guitar size={18} className="text-yellow-400" />;
      case 'Keys': return <Music size={18} className="text-green-400" />;
      default: return <File size={18} className="text-blue-400" />;
    }
  };
  
  return (
    <div>
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 mb-4">
        <h3 className="text-lg font-medium text-white">Files</h3>
        <div className="flex items-center gap-3">
          <div className="w-48">
            <Dropdown
              options={filterOptions}
              value={filter}
              onChange={setFilter}
              placeholder="Filter by category"
            />
          </div>
          <Button 
            size="sm" 
            icon={<Plus size={16} />}
            onClick={onAddFile}
          >
            Add File
          </Button>
        </div>
      </div>
      
      {files.length === 0 ? (
        <div className="bg-gray-800 border-2 border-dashed border-gray-700 rounded-lg p-6 text-center">
          <div className="mx-auto w-16 h-16 bg-gray-700/50 rounded-full flex items-center justify-center mb-3">
            <File size={24} className="text-gray-400" />
          </div>
          <h3 className="text-white font-medium mb-2">No files uploaded</h3>
          <p className="text-gray-400 text-sm mb-4">
            Upload audio files to start working on this track
          </p>
          <Button 
            size="sm" 
            icon={<Plus size={16} />}
            onClick={onAddFile}
          >
            Upload File
          </Button>
        </div>
      ) : filteredFiles.length === 0 ? (
        <div className="bg-gray-800 rounded-lg p-6 text-center">
          <h3 className="text-white font-medium mb-2">No files match the selected filter</h3>
          <p className="text-gray-400 text-sm">
            Try selecting a different category or view all files
          </p>
        </div>
      ) : (
        <div className="space-y-6">
          {Object.entries(groupedFiles).map(([category, categoryFiles]) => {
            if (categoryFiles.length === 0) return null;
            
            return (
              <div key={category}>
                <div className="flex items-center mb-2">
                  {getCategoryIcon(category)}
                  <h4 className="text-sm font-medium text-white ml-2">{category}</h4>
                </div>
                
                <div className="bg-gray-800 rounded-lg overflow-hidden">
                  {categoryFiles.map((file, index) => (
                    <div 
                      key={file.id} 
                      className={`
                        border-b border-gray-700 last:border-b-0
                        ${selectedFileId === file.id ? 'bg-gray-700/50' : 'hover:bg-gray-700/30'}
                        transition-colors cursor-pointer
                      `}
                      onClick={() => onFileSelect(file.id)}
                    >
                      <div className="flex items-center justify-between px-4 py-3">
                        <div className="flex items-center flex-1 min-w-0">
                          <div className="flex-shrink-0 mr-3">
                            {file.isPrimary && (
                              <div className="w-6 h-6 rounded-full bg-blue-600/20 flex items-center justify-center">
                                <Star size={14} className="text-blue-500" />
                              </div>
                            )}
                          </div>
                          
                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium text-white truncate">{file.name}</p>
                            <div className="flex items-center text-xs text-gray-400 mt-1">
                              <span>{formatDistanceToNow(new Date(file.uploadDate))}</span>
                              <span className="mx-2">•</span>
                              <span>{file.size}</span>
                            </div>
                          </div>
                        </div>
                        
                        <div className="flex items-center">
                          {selectedFileId === file.id && (
                            <span className="mr-3 text-blue-500">
                              <Check size={18} />
                            </span>
                          )}
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              // Open file actions menu
                            }}
                            className="text-gray-400 hover:text-white"
                          >
                            <MoreVertical size={16} />
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}