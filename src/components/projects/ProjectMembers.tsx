import React from 'react';
import { Modal } from '../ui/Modal';
import { Avatar } from '../ui/Avatar';
import { Button } from '../ui/Button';
import { UserPlus, Crown, MoreVertical } from 'lucide-react';
import { Project } from '../../types';

interface ProjectMembersProps {
  isOpen: boolean;
  onClose: () => void;
  project: Project;
  onInvite: () => void;
}

export function ProjectMembers({ isOpen, onClose, project, onInvite }: ProjectMembersProps) {
  const allMembers = [project.owner, ...project.members];
  
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Project Members"
      size="md"
    >
      <div className="space-y-4">
        <div className="space-y-2">
          {allMembers.map((member) => (
            <div 
              key={member.id} 
              className="flex items-center justify-between p-3 rounded-md hover:bg-gray-800"
            >
              <div className="flex items-center space-x-3">
                <Avatar src={member.avatar} alt={member.name} />
                <div>
                  <p className="text-sm font-medium text-white">
                    {member.name}
                    {member.id === project.owner.id && (
                      <span className="ml-2 inline-flex items-center">
                        <Crown size={14} className="text-yellow-500" />
                      </span>
                    )}
                  </p>
                  <p className="text-xs text-gray-400">{member.email}</p>
                </div>
              </div>
              
              {member.id !== project.owner.id && (
                <button className="text-gray-400 hover:text-white">
                  <MoreVertical size={16} />
                </button>
              )}
            </div>
          ))}
        </div>
        
        <div className="pt-4 border-t border-gray-700">
          <Button 
            variant="outline" 
            fullWidth
            icon={<UserPlus size={16} />}
            onClick={() => {
              onClose();
              onInvite();
            }}
          >
            Invite Members
          </Button>
        </div>
      </div>
    </Modal>
  );
}