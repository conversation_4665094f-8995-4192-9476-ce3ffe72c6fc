import React, { useState } from 'react';
import { Copy, Check, Mail, Link } from 'lucide-react';
import { Modal } from '../ui/Modal';
import { Input } from '../ui/Input';
import { Button } from '../ui/Button';

interface ShareProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  projectName: string;
}

export function ShareProjectModal({ isOpen, onClose, projectName }: ShareProjectModalProps) {
  const [email, setEmail] = useState('');
  const [copied, setCopied] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  
  const shareLink = `https://bandspace.app/invite/project-${Math.random().toString(36).substr(2, 9)}`;
  
  const handleCopyLink = () => {
    navigator.clipboard.writeText(shareLink)
      .then(() => {
        setCopied(true);
        setTimeout(() => setCopied(false), 2000);
      })
      .catch((err) => console.error('Failed to copy: ', err));
  };
  
  const handleSendInvite = (e: React.FormEvent) => {
    e.preventDefault();
    // Simulate sending invite
    setIsLoading(true);
    setTimeout(() => {
      setIsLoading(false);
      setEmail('');
      onClose();
    }, 1000);
  };
  
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title={`Share "${projectName}"`}
      size="md"
    >
      <div className="space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-300 mb-2">
            Project link
          </label>
          <div className="flex">
            <div className="flex-1 bg-gray-800 border border-gray-700 rounded-l-md px-4 py-2 text-sm text-gray-300 truncate">
              {shareLink}
            </div>
            <button
              type="button"
              onClick={handleCopyLink}
              className="flex items-center justify-center px-4 py-2 border border-l-0 border-gray-700 rounded-r-md bg-gray-700 text-white hover:bg-gray-600 transition-colors"
            >
              {copied ? <Check size={16} /> : <Copy size={16} />}
            </button>
          </div>
          <p className="mt-2 text-xs text-gray-400">
            Anyone with this link can join this project
          </p>
        </div>
        
        <div className="relative my-6">
          <div className="absolute inset-0 flex items-center">
            <div className="w-full border-t border-gray-700"></div>
          </div>
          <div className="relative flex justify-center">
            <span className="bg-gray-900 px-4 text-xs text-gray-400">Or</span>
          </div>
        </div>
        
        <form onSubmit={handleSendInvite}>
          <Input
            label="Email invite"
            placeholder="Enter email address"
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            icon={<Mail size={18} />}
          />
          
          <div className="flex justify-end space-x-3 mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              isLoading={isLoading}
              disabled={!email.trim()}
              icon={<Link size={16} />}
            >
              Send Invite
            </Button>
          </div>
        </form>
      </div>
    </Modal>
  );
}