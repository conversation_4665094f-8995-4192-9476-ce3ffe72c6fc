import React from 'react';
import { formatDistanceToNow } from '../../utils/dateUtils';
import { Project } from '../../types';
import { Avatar } from '../ui/Avatar';
import { Music } from 'lucide-react';

interface ProjectCardProps {
  project: Project;
  onClick: (projectId: string) => void;
}

export function ProjectCard({ project, onClick }: ProjectCardProps) {
  const { id, name, createdAt, members } = project;
  
  return (
    <div 
      className="bg-gray-800 border border-gray-700 rounded-lg overflow-hidden hover:border-blue-500 transition-all duration-200 cursor-pointer group"
      onClick={() => onClick(id)}
    >
      <div className="h-32 bg-gradient-to-r from-blue-900 to-indigo-900 p-4 flex items-center justify-center">
        <Music size={48} className="text-blue-400 opacity-80 transition-transform duration-200 group-hover:scale-110" />
      </div>
      
      <div className="p-4">
        <h3 className="text-lg font-medium text-white group-hover:text-blue-400 transition-colors">{name}</h3>
        <p className="text-sm text-gray-400 mt-1">
          Created {formatDistanceToNow(new Date(createdAt))}
        </p>
        
        <div className="mt-4 flex items-center justify-between">
          <div className="flex -space-x-2">
            {members.slice(0, 3).map((member) => (
              <Avatar 
                key={member.id}
                src={member.avatar}
                alt={member.name}
                size="sm"
                className="border-2 border-gray-800"
              />
            ))}
            {members.length > 3 && (
              <div className="w-8 h-8 rounded-full bg-gray-700 border-2 border-gray-800 flex items-center justify-center text-xs text-white">
                +{members.length - 3}
              </div>
            )}
          </div>
          
          <div className="bg-blue-600/20 rounded-full px-2.5 py-1 text-xs text-blue-400">
            {members.length + 1} member{members.length !== 0 ? 's' : ''}
          </div>
        </div>
      </div>
    </div>
  );
}