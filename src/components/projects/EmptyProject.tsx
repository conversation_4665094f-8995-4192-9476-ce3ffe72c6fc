import React from 'react';
import { Plus, Music } from 'lucide-react';
import { But<PERSON> } from '../ui/Button';

interface EmptyProjectProps {
  onCreateProject: () => void;
}

export function EmptyProject({ onCreateProject }: EmptyProjectProps) {
  return (
    <div className="bg-gray-800 border-2 border-dashed border-gray-700 rounded-lg p-8 flex flex-col items-center justify-center text-center">
      <div className="bg-gray-700/50 w-20 h-20 rounded-full flex items-center justify-center mb-4">
        <Music size={36} className="text-gray-400" />
      </div>
      
      <h3 className="text-lg font-medium text-white mb-2">No projects yet</h3>
      <p className="text-gray-400 mb-6 max-w-md">
        Start your musical journey by creating your first project. You can add tracks, invite collaborators, and share your work.
      </p>
      
      <Button
        onClick={onCreateProject}
        icon={<Plus size={18} />}
      >
        Create Project
      </Button>
    </div>
  );
}