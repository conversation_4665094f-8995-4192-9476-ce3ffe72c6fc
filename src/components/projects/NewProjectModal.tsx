import React, { useState } from 'react';
import { Modal } from '../ui/Modal';
import { Input } from '../ui/Input';
import { Button } from '../ui/Button';

interface NewProjectModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateProject: (name: string) => void;
  isLoading?: boolean;
}

export function NewProjectModal({ 
  isOpen, 
  onClose, 
  onCreateProject, 
  isLoading = false 
}: NewProjectModalProps) {
  const [projectName, setProjectName] = useState('');
  const [error, setError] = useState('');
  
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!projectName.trim()) {
      setError('Project name is required');
      return;
    }
    
    onCreateProject(projectName);
    setProjectName('');
    setError('');
  };
  
  return (
    <Modal
      isOpen={isOpen}
      onClose={onClose}
      title="Create New Project"
      size="sm"
    >
      <form onSubmit={handleSubmit}>
        <Input
          label="Project Name"
          placeholder="Enter project name"
          value={projectName}
          onChange={(e) => {
            setProjectName(e.target.value);
            if (error) setError('');
          }}
          error={error}
          autoFocus
        />
        
        <div className="flex justify-end space-x-3 mt-6">
          <Button
            type="button"
            variant="outline"
            onClick={onClose}
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            type="submit"
            isLoading={isLoading}
          >
            Create Project
          </Button>
        </div>
      </form>
    </Modal>
  );
}