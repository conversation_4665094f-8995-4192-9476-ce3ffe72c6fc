import React, { ReactNode } from 'react';
import { Header } from './Header';

interface MainLayoutProps {
  children: ReactNode;
  onNewProject?: () => void;
}

export function MainLayout({ children, onNewProject }: MainLayoutProps) {
  return (
    <div className="min-h-screen flex flex-col bg-gray-900 text-white">
      <Header onNewProject={onNewProject} />
      <main className="flex-1">
        {children}
      </main>
    </div>
  );
}