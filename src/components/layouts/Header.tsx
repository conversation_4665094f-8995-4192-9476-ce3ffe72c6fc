import React, { useState, useRef, useEffect } from 'react';
import { 
  Plus, 
  Bell, 
  Settings, 
  LogOut, 
  User as UserIcon, 
  ChevronDown,
  Music
} from 'lucide-react';
import { Avatar } from '../ui/Avatar';
import { Button } from '../ui/Button';
import { currentUser } from '../../data/mockData';

interface HeaderProps {
  onNewProject?: () => void;
}

export function Header({ onNewProject }: HeaderProps) {
  const [isProfileOpen, setIsProfileOpen] = useState(false);
  const [isNotificationsOpen, setIsNotificationsOpen] = useState(false);
  const profileRef = useRef<HTMLDivElement>(null);
  const notificationsRef = useRef<HTMLDivElement>(null);
  
  // Close dropdowns when clicking outside
  useEffect(() => {
    function handleClickOutside(event: MouseEvent) {
      if (
        profileRef.current && 
        !profileRef.current.contains(event.target as Node)
      ) {
        setIsProfileOpen(false);
      }
      
      if (
        notificationsRef.current && 
        !notificationsRef.current.contains(event.target as Node)
      ) {
        setIsNotificationsOpen(false);
      }
    }
    
    document.addEventListener('mousedown', handleClickOutside);
    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, []);
  
  const mockNotifications = [
    {
      id: '1',
      message: 'Jordan Smith added a new track to Summer EP',
      time: '15m ago'
    },
    {
      id: '2',
      message: 'Taylor Williams commented on Ocean Waves',
      time: '2h ago'
    },
    {
      id: '3',
      message: 'Morgan Lee invited you to collaborate on New Project',
      time: '1d ago'
    }
  ];
  
  return (
    <header className="bg-gray-900 border-b border-gray-800 py-3 px-4 sm:px-6">
      <div className="flex items-center justify-between">
        {/* Logo */}
        <div className="flex items-center">
          <div className="flex items-center text-blue-500">
            <Music size={28} />
            <span className="ml-2 text-xl font-bold text-white">BandSpace</span>
          </div>
        </div>
        
        {/* Actions */}
        <div className="flex items-center space-x-3">
          {onNewProject && (
            <Button 
              onClick={onNewProject}
              size="sm"
              icon={<Plus size={16} />}
              className="hidden sm:flex"
            >
              New Project
            </Button>
          )}
          
          {/* Notifications */}
          <div className="relative" ref={notificationsRef}>
            <button
              className="p-2 text-gray-400 hover:text-white rounded-full hover:bg-gray-800 transition-colors relative"
              onClick={() => {
                setIsNotificationsOpen(!isNotificationsOpen);
                setIsProfileOpen(false);
              }}
            >
              <Bell size={20} />
              <span className="absolute top-0 right-0 w-2 h-2 bg-blue-500 rounded-full"></span>
            </button>
            
            {isNotificationsOpen && (
              <div className="absolute right-0 mt-2 w-80 bg-gray-800 border border-gray-700 rounded-lg shadow-lg z-10 animate-in fade-in-50 zoom-in-95 duration-100">
                <div className="p-3 border-b border-gray-700">
                  <h3 className="text-sm font-medium text-white">Notifications</h3>
                </div>
                <ul className="max-h-96 overflow-y-auto">
                  {mockNotifications.map((notification) => (
                    <li key={notification.id} className="border-b border-gray-700 last:border-b-0">
                      <a href="#" className="block p-4 hover:bg-gray-700">
                        <p className="text-sm text-gray-300">{notification.message}</p>
                        <p className="text-xs text-gray-500 mt-1">{notification.time}</p>
                      </a>
                    </li>
                  ))}
                </ul>
                <div className="p-3 border-t border-gray-700 text-center">
                  <a href="#" className="text-sm text-blue-500 hover:text-blue-400">View all notifications</a>
                </div>
              </div>
            )}
          </div>
          
          {/* Profile */}
          <div className="relative" ref={profileRef}>
            <button
              className="flex items-center space-x-2 text-sm rounded-full hover:bg-gray-800 transition-colors p-1 pr-2"
              onClick={() => {
                setIsProfileOpen(!isProfileOpen);
                setIsNotificationsOpen(false);
              }}
            >
              <Avatar src={currentUser.avatar} alt={currentUser.name} size="sm" />
              <span className="hidden md:inline text-white">{currentUser.name}</span>
              <ChevronDown size={16} className="text-gray-400" />
            </button>
            
            {isProfileOpen && (
              <div className="absolute right-0 mt-2 w-56 bg-gray-800 border border-gray-700 rounded-lg shadow-lg z-10 animate-in fade-in-50 zoom-in-95 duration-100">
                <div className="p-3 border-b border-gray-700">
                  <p className="text-sm font-medium text-white">{currentUser.name}</p>
                  <p className="text-xs text-gray-400 truncate">{currentUser.email}</p>
                </div>
                <ul className="py-2">
                  <li>
                    <a href="#" className="flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">
                      <UserIcon size={16} className="mr-2 text-gray-400" />
                      Profile
                    </a>
                  </li>
                  <li>
                    <a href="#" className="flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">
                      <Settings size={16} className="mr-2 text-gray-400" />
                      Settings
                    </a>
                  </li>
                </ul>
                <div className="py-2 border-t border-gray-700">
                  <a href="#" className="flex items-center px-4 py-2 text-sm text-gray-300 hover:bg-gray-700">
                    <LogOut size={16} className="mr-2 text-gray-400" />
                    Sign out
                  </a>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </header>
  );
}