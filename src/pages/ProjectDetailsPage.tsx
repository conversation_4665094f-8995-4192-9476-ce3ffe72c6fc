import React, { useState } from 'react';
import { ChevronRight, Share2, Users, LogOut, Trash2 } from 'lucide-react';
import { MainLayout } from '../components/layouts/MainLayout';
import { TrackList } from '../components/tracks/TrackList';
import { Button } from '../components/ui/Button';
import { NewTrackModal } from '../components/tracks/NewTrackModal';
import { ShareProjectModal } from '../components/projects/ShareProjectModal';
import { ProjectMembers } from '../components/projects/ProjectMembers';
import { ConfirmationModal } from '../components/modals/ConfirmationModal';
import { useToast } from '../components/ui/Toast';
import { projects, tracks } from '../data/mockData';

export function ProjectDetailsPage() {
  // Simulate getting project by ID from URL
  const project = projects[0];
  const projectTracks = tracks.filter(track => track.projectId === project.id);
  
  const [showNewTrackModal, setShowNewTrackModal] = useState(false);
  const [showShareModal, setShowShareModal] = useState(false);
  const [showMembersModal, setShowMembersModal] = useState(false);
  const [showLeaveModal, setShowLeaveModal] = useState(false);
  const [showDeleteModal, setShowDeleteModal] = useState(false);
  const [isCreatingTrack, setIsCreatingTrack] = useState(false);
  const [tracksList, setTracksList] = useState(projectTracks);
  
  const { success, ToastContainer } = useToast();
  
  const handleCreateTrack = (name: string, category: string) => {
    setIsCreatingTrack(true);
    
    // Simulate API call
    setTimeout(() => {
      const newTrack = {
        id: `track-${Date.now()}`,
        name,
        category,
        createdAt: new Date().toISOString(),
        projectId: project.id,
        files: []
      };
      
      setTracksList([...tracksList, newTrack]);
      setIsCreatingTrack(false);
      setShowNewTrackModal(false);
      success(`Track "${name}" created successfully!`);
    }, 1000);
  };
  
  const handleTrackClick = (trackId: string) => {
    window.location.href = `/projects/${project.id}/tracks/${trackId}`;
  };
  
  const handleLeaveProject = () => {
    // Simulate API call
    setTimeout(() => {
      setShowLeaveModal(false);
      success('You have left the project.');
      window.location.href = '/projects';
    }, 1000);
  };
  
  const handleDeleteProject = () => {
    // Simulate API call
    setTimeout(() => {
      setShowDeleteModal(false);
      success('Project deleted successfully.');
      window.location.href = '/projects';
    }, 1000);
  };
  
  return (
    <MainLayout onNewProject={() => setShowNewTrackModal(true)}>
      <ToastContainer />
      
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 mb-8">
          <div>
            <div className="flex items-center text-sm text-gray-400 mb-1">
              <a href="/projects" className="hover:text-white transition-colors">Projects</a>
              <ChevronRight size={16} className="mx-1" />
              <span className="text-white">{project.name}</span>
            </div>
            <h1 className="text-2xl font-bold text-white">{project.name}</h1>
          </div>
          
          <div className="flex flex-wrap gap-3">
            <Button 
              variant="outline" 
              size="sm"
              icon={<Share2 size={16} />}
              onClick={() => setShowShareModal(true)}
            >
              Share
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              icon={<Users size={16} />}
              onClick={() => setShowMembersModal(true)}
            >
              Members
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              icon={<LogOut size={16} />}
              onClick={() => setShowLeaveModal(true)}
            >
              Leave
            </Button>
            <Button 
              variant="outline" 
              size="sm"
              icon={<Trash2 size={16} />}
              onClick={() => setShowDeleteModal(true)}
            >
              Delete
            </Button>
          </div>
        </div>
        
        <TrackList
          tracks={tracksList}
          onTrackClick={handleTrackClick}
          onNewTrack={() => setShowNewTrackModal(true)}
        />
      </div>
      
      <NewTrackModal
        isOpen={showNewTrackModal}
        onClose={() => setShowNewTrackModal(false)}
        onCreateTrack={handleCreateTrack}
        isLoading={isCreatingTrack}
      />
      
      <ShareProjectModal
        isOpen={showShareModal}
        onClose={() => setShowShareModal(false)}
        projectName={project.name}
      />
      
      <ProjectMembers
        isOpen={showMembersModal}
        onClose={() => setShowMembersModal(false)}
        project={project}
        onInvite={() => {
          setShowMembersModal(false);
          setShowShareModal(true);
        }}
      />
      
      <ConfirmationModal
        isOpen={showLeaveModal}
        onClose={() => setShowLeaveModal(false)}
        onConfirm={handleLeaveProject}
        title="Leave Project"
        message="Are you sure you want to leave this project? You will lose access to all tracks and files unless you're invited back."
        confirmText="Leave Project"
        isDestructive
      />
      
      <ConfirmationModal
        isOpen={showDeleteModal}
        onClose={() => setShowDeleteModal(false)}
        onConfirm={handleDeleteProject}
        title="Delete Project"
        message="Are you sure you want to delete this project? This action cannot be undone and all tracks and files will be permanently deleted."
        confirmText="Delete Project"
        isDestructive
      />
    </MainLayout>
  );
}