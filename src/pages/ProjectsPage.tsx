import React, { useState } from 'react';
import { MainLayout } from '../components/layouts/MainLayout';
import { ProjectCard } from '../components/projects/ProjectCard';
import { EmptyProject } from '../components/projects/EmptyProject';
import { NewProjectModal } from '../components/projects/NewProjectModal';
import { useToast } from '../components/ui/Toast';
import { projects } from '../data/mockData';

export function ProjectsPage() {
  const [showNewProjectModal, setShowNewProjectModal] = useState(false);
  const [isCreatingProject, setIsCreatingProject] = useState(false);
  const [projectsList, setProjectsList] = useState(projects);
  
  const { success, ToastContainer } = useToast();
  
  const handleCreateProject = (name: string) => {
    setIsCreatingProject(true);
    
    // Simulate API call
    setTimeout(() => {
      const newProject = {
        id: `project-${Date.now()}`,
        name,
        createdAt: new Date().toISOString(),
        owner: {
          id: 'user-1',
          name: '<PERSON>',
          email: '<EMAIL>',
          avatar: 'https://i.pravatar.cc/150?img=11'
        },
        members: []
      };
      
      setProjectsList([newProject, ...projectsList]);
      setIsCreatingProject(false);
      setShowNewProjectModal(false);
      success(`Project "${name}" created successfully!`);
    }, 1000);
  };
  
  const handleProjectClick = (projectId: string) => {
    window.location.href = `/projects/${projectId}`;
  };
  
  return (
    <MainLayout onNewProject={() => setShowNewProjectModal(true)}>
      <ToastContainer />
      
      <div className="container mx-auto px-4 py-8">
        <header className="mb-8">
          <h1 className="text-2xl font-bold text-white">My Projects</h1>
          <p className="text-gray-400 mt-1">Manage and organize your music projects</p>
        </header>
        
        {projectsList.length === 0 ? (
          <EmptyProject onCreateProject={() => setShowNewProjectModal(true)} />
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {projectsList.map((project) => (
              <ProjectCard
                key={project.id}
                project={project}
                onClick={handleProjectClick}
              />
            ))}
          </div>
        )}
      </div>
      
      <NewProjectModal
        isOpen={showNewProjectModal}
        onClose={() => setShowNewProjectModal(false)}
        onCreateProject={handleCreateProject}
        isLoading={isCreatingProject}
      />
    </MainLayout>
  );
}