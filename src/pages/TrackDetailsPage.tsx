import React, { useState, useEffect } from 'react';
import { ChevronRight, ChevronLeft, Plus } from 'lucide-react';
import { MainLayout } from '../components/layouts/MainLayout';
import { AudioPlayer } from '../components/ui/AudioPlayer';
import { TrackFiles } from '../components/tracks/TrackFiles';
import { Comments, MobileCommentsToggle } from '../components/tracks/Comments';
import { Button } from '../components/ui/Button';
import { FileUploadModal } from '../components/tracks/FileUploadModal';
import { useToast } from '../components/ui/Toast';
import { projects, tracks, files, comments } from '../data/mockData';
import { Comment, FileCategory } from '../types';

export function TrackDetailsPage() {
  // Simulate getting project and track by ID from URL
  const project = projects[0];
  const track = tracks[0];
  track.files = files; // Assign files to track for demo
  
  const [trackComments, setTrackComments] = useState<Comment[]>(comments);
  const [showFileUploadModal, setShowFileUploadModal] = useState(false);
  const [isUploadingFile, setIsUploadingFile] = useState(false);
  const [selectedFileId, setSelectedFileId] = useState<string | null>(
    track.files.length > 0 ? track.files[0].id : null
  );
  const [showMobileComments, setShowMobileComments] = useState(false);
  
  const { success, ToastContainer } = useToast();
  
  // Get selected file
  const selectedFile = track.files.find(file => file.id === selectedFileId);
  
  // Handle window resize for responsive layout
  useEffect(() => {
    const handleResize = () => {
      if (window.innerWidth >= 1024) {
        setShowMobileComments(false);
      }
    };
    
    window.addEventListener('resize', handleResize);
    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);
  
  const handleAddComment = (text: string) => {
    const newComment: Comment = {
      id: `comment-${Date.now()}`,
      text,
      createdAt: new Date().toISOString(),
      user: {
        id: 'user-1',
        name: 'Alex Johnson',
        email: '<EMAIL>',
        avatar: 'https://i.pravatar.cc/150?img=11'
      },
      trackId: track.id
    };
    
    setTrackComments([...trackComments, newComment]);
  };
  
  const handleFileUpload = (file: File, category: FileCategory) => {
    setIsUploadingFile(true);
    
    // Simulate API call
    setTimeout(() => {
      const newFile = {
        id: `file-${Date.now()}`,
        name: file.name,
        category,
        uploadDate: new Date().toISOString(),
        size: `${(file.size / 1024 / 1024).toFixed(2)} MB`,
        url: '/mock-audio.mp3',
        isPrimary: track.files.filter(f => f.category === category).length === 0
      };
      
      track.files.push(newFile);
      setSelectedFileId(newFile.id);
      setIsUploadingFile(false);
      setShowFileUploadModal(false);
      success(`File "${file.name}" uploaded successfully!`);
    }, 1500);
  };
  
  return (
    <MainLayout>
      <ToastContainer />
      
      <div className="container mx-auto px-4 py-8">
        <div className="flex items-center text-sm text-gray-400 mb-2">
          <a href="/projects" className="hover:text-white transition-colors">Projects</a>
          <ChevronRight size={16} className="mx-1" />
          <a href={`/projects/${project.id}`} className="hover:text-white transition-colors">{project.name}</a>
          <ChevronRight size={16} className="mx-1" />
          <span className="text-white">{track.name}</span>
        </div>
        
        <div className="lg:flex lg:gap-8">
          {/* Main content */}
          <div className={`lg:flex-1 ${showMobileComments ? 'hidden' : 'block'} lg:block`}>
            <h1 className="text-2xl font-bold text-white mb-6">{track.name}</h1>
            
            {/* Audio player */}
            <div className="mb-8">
              {selectedFile ? (
                <AudioPlayer 
                  audioUrl={selectedFile.url} 
                  fileName={selectedFile.name} 
                />
              ) : (
                <div className="bg-gray-800 rounded-lg p-8 text-center">
                  <p className="text-gray-400 mb-4">No audio file selected</p>
                  <Button 
                    size="sm" 
                    icon={<Plus size={16} />}
                    onClick={() => setShowFileUploadModal(true)}
                  >
                    Upload File
                  </Button>
                </div>
              )}
            </div>
            
            {/* Files */}
            <TrackFiles 
              files={track.files}
              onFileSelect={setSelectedFileId}
              onAddFile={() => setShowFileUploadModal(true)}
              selectedFileId={selectedFileId}
            />
          </div>
          
          {/* Comments section */}
          <div 
            className={`
              lg:w-96 lg:border-l border-gray-700 
              ${showMobileComments ? 'block' : 'hidden'} lg:block
              fixed top-0 right-0 bottom-0 lg:relative z-20
              bg-gray-900 lg:bg-transparent
              w-full
            `}
          >
            {showMobileComments && (
              <div className="flex items-center lg:hidden border-b border-gray-700 p-4">
                <button
                  onClick={() => setShowMobileComments(false)}
                  className="text-gray-400 hover:text-white mr-3"
                >
                  <ChevronLeft size={20} />
                </button>
                <h2 className="text-lg font-medium text-white">Back to Track</h2>
              </div>
            )}
            
            <div className="h-full lg:h-[calc(100vh-7rem)] lg:sticky lg:top-20">
              <Comments 
                comments={trackComments}
                onAddComment={handleAddComment}
                isMobileView={showMobileComments}
                onToggleVisibility={() => setShowMobileComments(false)}
              />
            </div>
          </div>
        </div>
      </div>
      
      {/* Mobile comments toggle */}
      <div className="lg:hidden">
        <MobileCommentsToggle 
          onClick={() => setShowMobileComments(!showMobileComments)} 
          isVisible={showMobileComments}
        />
      </div>
      
      <FileUploadModal
        isOpen={showFileUploadModal}
        onClose={() => setShowFileUploadModal(false)}
        onUpload={handleFileUpload}
        isLoading={isUploadingFile}
      />
    </MainLayout>
  );
}