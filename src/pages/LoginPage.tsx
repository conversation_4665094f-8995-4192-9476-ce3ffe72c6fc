import React, { useState } from 'react';
import { AuthForm } from '../components/auth/AuthForm';
import { useToast } from '../components/ui/Toast';
import { Music } from 'lucide-react';

export function LoginPage() {
  const [isLoading, setIsLoading] = useState(false);
  const [mode, setMode] = useState<'login' | 'register'>('login');
  const { success, error, ToastContainer } = useToast();
  
  const handleSubmit = (data: { name?: string; email: string; password: string }) => {
    setIsLoading(true);
    
    // Simulate API call
    setTimeout(() => {
      if (mode === 'login') {
        // Simulate successful login
        success('Successfully logged in!');
        window.location.href = '/projects';
      } else {
        // Simulate successful registration
        success('Account created successfully! Please sign in.');
        setMode('login');
      }
      setIsLoading(false);
    }, 1500);
  };
  
  return (
    <div className="min-h-screen bg-gray-900 flex flex-col">
      <ToastContainer />
      
      <div className="flex min-h-screen">
        {/* Left panel (hidden on mobile) */}
        <div className="hidden lg:flex lg:w-1/2 bg-gradient-to-br from-blue-900 to-indigo-900 p-12 flex-col justify-between">
          <div className="flex items-center text-white">
            <Music size={32} />
            <span className="ml-2 text-2xl font-bold">BandSpace</span>
          </div>
          
          <div className="space-y-6 max-w-lg">
            <h1 className="text-4xl font-bold text-white">Make music together, anywhere.</h1>
            <p className="text-lg text-blue-100">
              Collaborate on tracks, share files, and produce amazing music with your team — all in one space.
            </p>
            
            <div className="grid grid-cols-2 gap-6 pt-8">
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <h3 className="text-white font-medium mb-1">Seamless Collaboration</h3>
                <p className="text-sm text-blue-100">Work together in real-time, no matter where you are.</p>
              </div>
              
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <h3 className="text-white font-medium mb-1">Organized Workflow</h3>
                <p className="text-sm text-blue-100">Keep all your projects and tracks in one place.</p>
              </div>
              
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <h3 className="text-white font-medium mb-1">Feedback System</h3>
                <p className="text-sm text-blue-100">Comment on tracks to provide detailed feedback.</p>
              </div>
              
              <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                <h3 className="text-white font-medium mb-1">Secure Sharing</h3>
                <p className="text-sm text-blue-100">Control who has access to your music and files.</p>
              </div>
            </div>
          </div>
          
          <div className="text-sm text-blue-200">
            © 2025 BandSpace. All rights reserved.
          </div>
        </div>
        
        {/* Right panel */}
        <div className="w-full lg:w-1/2 flex items-center justify-center p-8">
          <div className="w-full max-w-md">
            <div className="lg:hidden flex items-center text-blue-500 mb-8">
              <Music size={28} />
              <span className="ml-2 text-xl font-bold text-white">BandSpace</span>
            </div>
            
            <h2 className="text-2xl font-bold text-white mb-2">
              {mode === 'login' ? 'Welcome back!' : 'Create your account'}
            </h2>
            <p className="text-gray-400 mb-8">
              {mode === 'login' 
                ? 'Sign in to continue to your projects.'
                : 'Join BandSpace to start collaborating on music projects.'}
            </p>
            
            <AuthForm 
              mode={mode}
              onSubmit={handleSubmit}
              isLoading={isLoading}
            />
            
            <p className="mt-6 text-center text-sm text-gray-400">
              {mode === 'login' 
                ? "Don't have an account? "
                : "Already have an account? "}
              <button 
                onClick={() => setMode(mode === 'login' ? 'register' : 'login')}
                className="text-blue-500 hover:text-blue-400 font-medium"
              >
                {mode === 'login' ? 'Sign up' : 'Sign in'}
              </button>
            </p>
          </div>
        </div>
      </div>
    </div>
  );
}