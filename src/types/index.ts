export interface User {
  id: string;
  name: string;
  email: string;
  avatar: string;
}

export interface Project {
  id: string;
  name: string;
  createdAt: string;
  owner: User;
  members: User[];
}

export interface Track {
  id: string;
  name: string;
  category: string;
  createdAt: string;
  projectId: string;
  files: TrackFile[];
}

export interface TrackFile {
  id: string;
  name: string;
  category: string;
  uploadDate: string;
  size: string;
  url: string;
  isPrimary: boolean;
}

export interface Comment {
  id: string;
  text: string;
  createdAt: string;
  user: User;
  trackId: string;
}

export type FileCategory = 'Vocals' | 'Drums' | 'Bass' | 'Guitar' | 'Keys' | 'Other';