import { Project, Track, TrackFile, Comment, User, FileCategory } from '../types';

export const currentUser: User = {
  id: 'user-1',
  name: '<PERSON>',
  email: '<EMAIL>',
  avatar: 'https://i.pravatar.cc/150?img=11'
};

export const users: User[] = [
  currentUser,
  {
    id: 'user-2',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://i.pravatar.cc/150?img=12'
  },
  {
    id: 'user-3',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://i.pravatar.cc/150?img=13'
  },
  {
    id: 'user-4',
    name: '<PERSON>',
    email: '<EMAIL>',
    avatar: 'https://i.pravatar.cc/150?img=14'
  }
];

export const projects: Project[] = [
  {
    id: 'project-1',
    name: 'Summer EP',
    createdAt: '2025-03-15T10:30:00Z',
    owner: currentUser,
    members: [users[1], users[2]]
  },
  {
    id: 'project-2',
    name: 'Acoustic Sessions',
    createdAt: '2025-02-20T14:45:00Z',
    owner: currentUser,
    members: [users[3]]
  },
  {
    id: 'project-3',
    name: 'Collaboration with Neon',
    createdAt: '2025-01-10T09:15:00Z',
    owner: users[1],
    members: [currentUser, users[2]]
  }
];

export const categories: FileCategory[] = [
  'Vocals',
  'Drums',
  'Bass',
  'Guitar',
  'Keys',
  'Other'
];

export const tracks: Track[] = [
  {
    id: 'track-1',
    name: 'Sunset Dreams',
    category: 'Song',
    createdAt: '2025-03-20T11:30:00Z',
    projectId: 'project-1',
    files: []
  },
  {
    id: 'track-2',
    name: 'Ocean Waves',
    category: 'Instrumental',
    createdAt: '2025-03-22T15:45:00Z',
    projectId: 'project-1',
    files: []
  },
  {
    id: 'track-3',
    name: 'Midnight Drive',
    category: 'Song',
    createdAt: '2025-02-25T13:15:00Z',
    projectId: 'project-2',
    files: []
  }
];

export const files: TrackFile[] = [
  {
    id: 'file-1',
    name: 'vocals-take1.mp3',
    category: 'Vocals',
    uploadDate: '2025-03-21T14:30:00Z',
    size: '4.2 MB',
    url: '/mock-audio.mp3',
    isPrimary: true
  },
  {
    id: 'file-2',
    name: 'vocals-take2.mp3',
    category: 'Vocals',
    uploadDate: '2025-03-21T15:45:00Z',
    size: '4.5 MB',
    url: '/mock-audio.mp3',
    isPrimary: false
  },
  {
    id: 'file-3',
    name: 'guitar-rhythm.mp3',
    category: 'Guitar',
    uploadDate: '2025-03-22T10:15:00Z',
    size: '5.1 MB',
    url: '/mock-audio.mp3',
    isPrimary: true
  },
  {
    id: 'file-4',
    name: 'bass-line.mp3',
    category: 'Bass',
    uploadDate: '2025-03-23T09:30:00Z',
    size: '3.8 MB',
    url: '/mock-audio.mp3',
    isPrimary: true
  },
  {
    id: 'file-5',
    name: 'drum-beat.mp3',
    category: 'Drums',
    uploadDate: '2025-03-24T11:00:00Z',
    size: '4.7 MB',
    url: '/mock-audio.mp3',
    isPrimary: true
  }
];

// Update track with files
tracks[0].files = [files[0], files[1], files[2], files[3], files[4]];

export const comments: Comment[] = [
  {
    id: 'comment-1',
    text: 'Love the vocal take! The chorus sounds amazing.',
    createdAt: '2025-03-21T16:30:00Z',
    user: users[1],
    trackId: 'track-1'
  },
  {
    id: 'comment-2',
    text: 'I think we should try a different arrangement for the bridge section.',
    createdAt: '2025-03-22T10:45:00Z',
    user: users[2],
    trackId: 'track-1'
  },
  {
    id: 'comment-3',
    text: 'Added a new bass line. Let me know what you think!',
    createdAt: '2025-03-23T14:15:00Z',
    user: currentUser,
    trackId: 'track-1'
  },
  {
    id: 'comment-4',
    text: 'The drum pattern is perfect. Really ties everything together.',
    createdAt: '2025-03-24T12:30:00Z',
    user: users[3],
    trackId: 'track-1'
  }
];