import React from 'react';
import { LoginPage } from './pages/LoginPage';
import { ProjectsPage } from './pages/ProjectsPage';
import { ProjectDetailsPage } from './pages/ProjectDetailsPage';
import { TrackDetailsPage } from './pages/TrackDetailsPage';

function App() {
  // Very simple "router" for the prototype
  const path = window.location.pathname;
  
  // Render the appropriate page based on the URL
  const renderPage = () => {
    if (path === '/' || path === '/login') {
      return <LoginPage />;
    } else if (path === '/projects') {
      return <ProjectsPage />;
    } else if (path.match(/^\/projects\/[^\/]+$/)) {
      return <ProjectDetailsPage />;
    } else if (path.match(/^\/projects\/[^\/]+\/tracks\/[^\/]+$/)) {
      return <TrackDetailsPage />;
    }
    
    // Default to login page
    return <LoginPage />;
  };
  
  return (
    <div className="min-h-screen bg-gray-900 text-white">
      {renderPage()}
    </div>
  );
}

export default App;