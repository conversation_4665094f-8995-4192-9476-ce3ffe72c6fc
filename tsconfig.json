{
  "extends": "./.svelte-kit/tsconfig.json",
  "compilerOptions": {
    "allowJs": true,
    "checkJs": false,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true,
    "resolveJsonModule": true,
    "skipLibCheck": true,
    "sourceMap": true,
    "strict": true,
    "allowUnreachableCode": true,
    "moduleResolution": "bundler",
    "baseUrl": ".", // Dodajemy baseUrl dla relatywnych ścieżek
    "paths": { // Explicitne mapowanie dla $app/*
      "$app/*": [".svelte-kit/types/$app/*"],
      "$lib": ["./src/lib"],
      "$lib/*": ["./src/lib/*"]
    }
  },
  "include": [
    "src/**/*.d.ts",
    "src/**/*.ts",
    "src/**/*.js",
    "src/**/*.svelte",
    ".svelte-kit/ambient.d.ts" // Dodajemy typy ambientne
  ]
}