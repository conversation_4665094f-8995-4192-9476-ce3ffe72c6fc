{"name": "bandspace", "version": "0.4.1", "private": true, "scripts": {"dev": "vite dev --host", "dev:debug": "node --inspect=9229 node_modules/.bin/vite dev --port 5173 --host", "build": "vite build", "preview": "vite preview", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch"}, "devDependencies": {"@sveltejs/adapter-auto": "^3.0.0", "@sveltejs/adapter-vercel": "^5.6.3", "@sveltejs/kit": "^2.17.3", "@sveltejs/vite-plugin-svelte": "^4.0.0", "@tsconfig/svelte": "^5.0.2", "autoprefixer": "^10.4.14", "postcss": "^8.4.24", "supabase": "^2.12.1", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwind-scrollbar": "^3.1.0", "tailwindcss": "^3.3.2", "tslib": "^2.4.1", "typescript": "^5.8.2", "vite": "^5.4.4"}, "type": "module", "dependencies": {"@careswitch/svelte-data-table": "^0.6.3", "@supabase/ssr": "^0.5.2", "@supabase/supabase-js": "^2.49.1", "@tabler/icons-svelte": "^3.31.0", "@uppy/core": "^4.4.2", "@uppy/status-bar": "^4.1.2", "@uppy/svelte": "^4.3.0", "@uppy/tus": "^4.2.2", "date-fns": "^4.1.0", "lucide-svelte": "^0.511.0"}}